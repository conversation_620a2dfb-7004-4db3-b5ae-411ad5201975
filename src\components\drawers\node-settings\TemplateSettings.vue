<template>
  <div class="template-settings">
    <div class="section-title">模板内容</div>
    <el-form :model="localForm" ref="formRef" label-width="90px" :rules="rules" class="settings-form">
      <el-form-item label="模板内容" prop="content" required>
        <el-input
          type="textarea"
          v-model="localForm.content"
          :rows="8"
          placeholder="请输入模板内容"
          class="template-input"
        />
      </el-form-item>
    </el-form>

    <!-- 保存按钮 -->
    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplateSettings',
  props: {
    value: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localForm: {
        content: this.value.content || ''
      },
      rules: {
        content: [
          { required: true, message: '模板内容不能为空', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    value: {
      handler(val) {
        this.localForm.content = val.content || '';
      },
      deep: true
    },
    localForm: {
      handler(val) {
        this.$emit('input', { ...val });
      },
      deep: true
    }
  },
  methods: {
    // 保存设置
    saveSettings() {
      console.log('[TemplateSettings] 用户点击保存按钮');
      console.log('[TemplateSettings] 保存的数据:', this.localForm);

      this.$emit('save', this.localForm);

      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    }
  }
};
</script>

<style scoped>
.template-settings {
  padding: 0 20px 24px 20px;
  background: #fff;
}
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin: 20px 0 12px 0;
  border-left: 3px solid #6366F1;
  padding-left: 10px;
  line-height: 1.2;
}
.settings-form {
  margin-top: 0;
}
.template-input >>> .el-textarea__inner {
  font-size: 14px;
  min-height: 120px;
  resize: vertical;
  border-radius: 6px;
  padding: 10px 12px;
  line-height: 1.7;
}
.el-form-item {
  margin-bottom: 18px;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}
</style>