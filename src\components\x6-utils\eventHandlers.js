/**
 * 事件处理相关功能模块
 */
import { calculateMenuPosition } from './menuHandlers';
import { isResizeHandleClicked } from './nodeUtils';
import { startResizeLoopContent } from './loopUtils';
import { checkNodeInLoop } from './loopUtils';

/**
 * 初始化图表事件处理
 * @param {Object} graph - 图表实例
 * @param {Object} context - 组件上下文
 */
export function initGraphEvents(graph, context) {
  // 添加专门处理菜单按钮点击的事件
  graph.on('node:click', ({ node, e, view }) => {
    // 如果是开始节点，不处理菜单
    if (node.id === 'start-node') return;

    console.log('Node clicked:', node.id);

    const nodeData = node.getData() || {};

    // 🔧 对于注释节点，完全跳过菜单处理，只处理选中逻辑
    if (nodeData.type === 'comment') {
      // 更新当前选中节点
      context.selectedNode = {
        id: node.id,
        type: nodeData.type || nodeData.modelType || '',
        name: nodeData.name || nodeData.modelName || '',
        getData: () => node.getData()
      };

      // 选中节点
      context.selectNode(node);

      // 阻止事件冒泡
      e.stopPropagation();
      return;
    }

    // 检查是否点击了菜单按钮（非注释节点）
    const menuButtonEl = view.findOne('text[data-shape="menuButton"]');
    if (menuButtonEl) {
      // 获取菜单按钮的包围盒
      const menuButtonBBox = menuButtonEl.getBBox();
      // 获取点击位置（相对于节点）
      const { x, y } = view.graph.clientToLocal(e.clientX, e.clientY);

      // 检查点击是否在菜单按钮区域内
      if (x >= menuButtonBBox.x &&
          x <= menuButtonBBox.x + menuButtonBBox.width &&
          y >= menuButtonBBox.y &&
          y <= menuButtonBBox.y + menuButtonBBox.height) {
        console.log('Menu button clicked for node:', node.id);

        // 非注释节点，立即触发编辑
        context.nodeMenu.currentNodeId = node.id;
        context.editCurrentNode();

        // 阻止事件冒泡
        e.stopPropagation();
        return;
      }
    }

    // 更新当前选中节点
    context.selectedNode = {
      id: node.id,
      type: nodeData.type || nodeData.modelType || '',
      name: nodeData.name || nodeData.modelName || '',
      getData: () => node.getData()
    };

    // 选中节点
    context.selectNode(node);

    // 阻止事件冒泡
    e.stopPropagation();
  });

  // 🔧 新增：添加节点双击事件监听 - 直接编辑注释
  graph.on('node:dblclick', ({ node, e }) => {
    // 如果是开始节点，不处理
    if (node.id === 'start-node') return;

    const nodeData = node.getData() || {};

    // 如果是注释节点，触发内联编辑
    if (nodeData.type === 'comment') {
      console.log('[双击编辑] 触发注释内联编辑:', node.id);
      console.log('[双击编辑] context对象:', context);
      console.log('[双击编辑] startInlineEdit方法存在:', typeof context.startInlineEdit);

      // 触发内联编辑
      if (context.startInlineEdit) {
        console.log('[双击编辑] 调用startInlineEdit方法');
        context.startInlineEdit(node);
      } else {
        console.error('[双击编辑] startInlineEdit方法不存在');
      }

      // 阻止事件冒泡
      e.stopPropagation();
      e.preventDefault();
    }
  });

  // 添加画布点击事件监听，用于取消选择
  graph.on('blank:click', () => {
    console.log('Canvas clicked, clearing selection');
    context.nodeMenu.visible = false;
    
    context.clearSelection();
    
    // 通知父组件取消节点选择
    context.$emit('node-selected', null);
  });
  
  // 修改节点鼠标进入事件监听
  graph.on('node:mouseenter', ({ node, view }) => {
    // 如果是开始节点，不处理菜单
    if (node.id === 'start-node') return;

    const nodeData = node.getData() || {};

    // 🔧 对于注释节点，只设置鼠标样式，不显示抽屉菜单
    if (nodeData.type === 'comment') {
      const nodeElement = view.container;
      if (nodeElement) {
        nodeElement.style.cursor = 'pointer';
        nodeElement.title = '双击编辑注释内容';
      }
      return; // 直接返回，不显示抽屉菜单
    }

    // 显示菜单（非注释节点）
    context.nodeMenu.currentNodeId = node.id;
    context.nodeMenu.visible = true;

    // 计算菜单位置
    const { top, left } = calculateMenuPosition(graph, node);

    // 设置菜单位置
    context.nodeMenu.top = top;
    context.nodeMenu.left = left;

    // 尝试获取菜单按钮元素并高亮
    const menuButtonEl = view.findOne('text[data-shape="menuButton"]');
    if (menuButtonEl && menuButtonEl.node) {
      try {
        // 使用原生DOM方法设置样式
        menuButtonEl.node.setAttribute('fill', '#409EFF');
      } catch (err) {
        console.error('Failed to set menu button color on mouseenter', err);
      }
    }
  });
  
  // 修改节点鼠标离开事件监听
  graph.on('node:mouseleave', ({ node, view }) => {
    // 如果是开始节点，不处理菜单
    if (node.id === 'start-node') return;

    const nodeData = node.getData() || {};

    // 🔧 对于注释节点，重置鼠标样式
    if (nodeData.type === 'comment') {
      const nodeElement = view.container;
      if (nodeElement) {
        nodeElement.style.cursor = 'default';
        nodeElement.title = '';
      }
    }

    // 恢复菜单按钮样式
    const menuButtonEl = view.findOne('text[data-shape="menuButton"]');
    if (menuButtonEl && menuButtonEl.node) {
      try {
        // 使用原生DOM方法设置样式
        menuButtonEl.node.setAttribute('fill', '#606266');
      } catch (err) {
        console.error('Failed to set menu button color on mouseleave', err);
      }
    }

    // 如果鼠标不在菜单上，延迟关闭菜单
    if (!context.isMouseOverMenu && context.nodeMenu.currentNodeId === node.id) {
      if (context.menuCloseTimeout) {
        clearTimeout(context.menuCloseTimeout);
      }
      context.menuCloseTimeout = setTimeout(() => {
        context.nodeMenu.visible = false;
      }, 150);
    }
  });
  
  // 添加节点鼠标移动事件监听
  graph.on('node:mousemove', ({ node, view }) => {
    // 如果是开始节点，不处理菜单
    if (node.id === 'start-node') return;

    // 如果正在拖拽中，不显示菜单
    if (context.isDragging) return;

    const nodeData = node.getData() || {};

    // 🔧 对于注释节点，不显示抽屉菜单
    if (nodeData.type === 'comment') {
      return; // 直接返回，不显示抽屉菜单
    }

    // 显示菜单（非注释节点）
    context.nodeMenu.currentNodeId = node.id;
    context.nodeMenu.visible = true;

    // 计算菜单位置
    const { top, left } = calculateMenuPosition(graph, node);

    // 设置菜单位置
    context.nodeMenu.top = top;
    context.nodeMenu.left = left;

    // 尝试获取菜单按钮元素
    const menuButtonEl = view.findOne('text[data-shape="menuButton"]');
    if (menuButtonEl && menuButtonEl.node) {
      try {
        // 使用原生DOM方法设置样式
        menuButtonEl.node.setAttribute('fill', '#409EFF');
      } catch (err) {
        console.error('Failed to set menu button color on mousemove', err);
      }
    }
  });
  
  // 添加节点鼠标按下事件，标记开始拖拽
  graph.on('node:mousedown', ({ node, e }) => {
    context.isDragging = true;
    // 隐藏菜单
    context.nodeMenu.visible = false;
    
    // 如果是普通节点，记录当前所在的循环（如果有）
    const nodeData = node.getData();
    if (!nodeData || (!nodeData.isLoop && !nodeData.isLoopContent)) {
      context.currentDraggingNodeId = node.id;
    }
    
    // 检查是否点击了调整大小的手柄
    if (nodeData && nodeData.isLoopContent && isResizeHandleClicked(e, node, graph)) {
      startResizeLoopContent(e, node, graph);
      e.stopPropagation();
    }
  });
  
  // 添加节点鼠标释放事件，标记结束拖拽
  graph.on('node:mouseup', ({ node }) => {
    context.isDragging = false;
    
    // 如果是普通节点，检查它是否在循环内
    const nodeData = node.getData();
    if (!nodeData || (!nodeData.isLoop && !nodeData.isLoopContent)) {
      setTimeout(() => {
        checkNodeInLoop(graph);
      }, 50);
    }
  });
  
  // 添加空白区域鼠标释放事件，确保拖拽结束
  graph.on('blank:mouseup', () => {
    context.isDragging = false;
    // 检查节点是否在循环内
    checkNodeInLoop(graph);
  });
  
  // 添加键盘删除键事件处理
  try {
    if (typeof graph.bindKey === 'function') {
  graph.bindKey('delete', () => {
    if (context.selectedNode) {
      context.deleteSelectedNode();
    }
  });
    } else {
      console.warn('graph.bindKey 不是一个函数，可能是X6版本不兼容');
    }
  } catch (error) {
    console.error('绑定键盘事件出错:', error);
  }
  
  // 添加节点移动事件监听
  graph.on('node:change:position', ({ node, previous }) => {
    // 如果是循环节点，处理其子节点的移动
    const nodeData = node.getData();
    if (nodeData && nodeData.isLoop) {
      const current = node.position();
      const deltaX = current.x - previous.x;
      const deltaY = current.y - previous.y;
      
      // 获取内容区域节点并移动
      const contentId = `${node.id}-content`;
      const contentNode = graph.getCellById(contentId);
      if (contentNode) {
        contentNode.position(current.x, current.y + 65); // 减小间隔
      }
      
      // 移动所有子节点
      if (nodeData.childNodes && Array.isArray(nodeData.childNodes)) {
        nodeData.childNodes.forEach(childId => {
          const childNode = graph.getCellById(childId);
          if (childNode) {
            const pos = childNode.position();
            childNode.position(pos.x + deltaX, pos.y + deltaY);
          }
        });
      }
    } else if (nodeData && nodeData.isLoopContent) {
      // 如果是循环内容区域，同步移动循环头部节点
      const parentId = nodeData.parentId;
      if (parentId) {
        const loopNode = graph.getCellById(parentId);
        if (loopNode) {
          const current = node.position();
          loopNode.position(current.x, current.y - 65); // 调整位置关系
        }
      }
    } else if (!nodeData || (!nodeData.isLoop && !nodeData.isLoopContent)) {
      // 如果是普通节点（非循环内容区域），在移动后检查它是否在循环内
      setTimeout(() => {
        checkNodeInLoop(graph);
      }, 50);
    }
  });
  
  // 监听节点大小变化
  graph.on('node:change:size', ({ node, previous, current }) => {
    const nodeData = node.getData();
    
    // 如果是循环节点，同步调整内容区域大小
    if (nodeData && nodeData.isLoop) {
      const contentId = `${node.id}-content`;
      const contentNode = graph.getCellById(contentId);
      if (contentNode) {
        // 只同步宽度变化，保持高度不变
        if (previous.width !== current.width) {
          contentNode.resize(current.width, contentNode.size().height);
        }
      }
    }
    // 如果是循环内容区域，同步调整循环头部节点宽度
    else if (nodeData && nodeData.isLoopContent) {
      const parentId = nodeData.parentId;
      if (parentId) {
        const loopNode = graph.getCellById(parentId);
        if (loopNode) {
          // 只同步宽度变化，保持高度不变
          if (previous.width !== current.width) {
            loopNode.resize(current.width, loopNode.size().height);
          }
          
          // 调整IterationItem节点位置，确保其始终保持在内容区域的固定相对位置
          const iterationItemId = `${parentId}-item`;
          const iterationItem = graph.getCellById(iterationItemId);
          if (iterationItem) {
            const contentPos = node.position();
            iterationItem.position(
              contentPos.x + 60,  // 距离内容区域左边60px
              contentPos.y + 60   // 距离内容区域顶部60px
            );
          }
        }
      }
    }
  });
}

/**
 * 处理拖拽经过事件
 * @param {Event} event - 拖拽事件对象
 */
export function handleDragOver(event) {
  // 允许放置
  event.preventDefault();
}

/**
 * 处理拖拽放置事件
 * @param {Event} event - 拖拽事件对象
 * @param {Object} context - 组件上下文
 * @param {Function} createNodeFn - 创建节点的函数
 */
export function handleDrop(event, context, createNodeFn) {
  event.preventDefault();
  
  // 获取模型数据
  try {
    // 尝试从多种MIME类型获取数据
    let jsonData = event.dataTransfer.getData('application/json');
    
    // 如果application/json为空，尝试text/plain
    if (!jsonData || jsonData.trim() === '') {
      jsonData = event.dataTransfer.getData('text/plain');
    }
    
    console.log('Received JSON data:', jsonData);
    
    // 检查数据是否为空
    if (!jsonData || jsonData.trim() === '') {
      console.error('拖放处理错误: 未收到有效的JSON数据');
      return;
    }
    
    // 尝试备用方案：如果有可用的数据类型，获取第一个可用的
    if (!jsonData || jsonData.trim() === '') {
      const types = event.dataTransfer.types;
      if (types && types.length > 0) {
        for (const type of types) {
          const data = event.dataTransfer.getData(type);
          if (data && data.trim() !== '') {
            jsonData = data;
            console.log('使用备用数据类型:', type);
            break;
          }
        }
      }
    }
    
    // 最终检查
    if (!jsonData || jsonData.trim() === '') {
      console.error('拖放处理错误: 无法获取任何有效数据');
      context.$message({
        message: '拖放失败: 无法获取节点数据',
        type: 'error',
        duration: 2000
      });
      return;
    }
    
    // 尝试解析JSON，处理特殊字符
    try {
      // 特殊处理：替换掉可能导致JSON解析错误的字符
      let cleanJsonData = jsonData;
      
      // 替换菜单按钮字符 '⋮'，这个字符在JSON中是非法的
      cleanJsonData = cleanJsonData.replace(/⋮/g, '"menu"');
      
      // 尝试解析清理后的JSON
      const modelData = JSON.parse(cleanJsonData);
      if (!modelData) {
        throw new Error('解析后的数据为空');
      }
      
      console.log('解析后的模型数据:', modelData);
    
      // 获取放置的画布坐标
      const { offsetX, offsetY } = event;
      
      // 转换为画布坐标系
      const point = context.graph.clientToLocal(offsetX, offsetY);
      
      // 创建节点
      createNodeFn(modelData, point.x, point.y);
      
      // 在节点创建后检查它是否在循环内
      checkNodeInLoop(context.graph);
      
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      context.$message({
        message: `JSON解析错误: ${parseError.message}`,
        type: 'error',
        duration: 3000
      });
    }
  } catch (error) {
    console.error('拖放处理错误:', error);
    context.$message({
      message: '拖放处理错误，请重试',
      type: 'error',
      duration: 2000
    });
  }
}

/**
 * 处理全局点击事件，用于关闭菜单
 * @param {Event} e - 点击事件对象
 * @param {Object} context - 组件上下文
 */
export function handleGlobalClick(e, context) {
  // 检查点击是否在菜单区域外
  const menuEl = document.querySelector('.node-hover-menu');
  if (menuEl && !menuEl.contains(e.target)) {
    context.nodeMenu.visible = false;
  }
}

/**
 * 处理键盘事件
 * @param {Event} e - 键盘事件对象
 * @param {Object} context - 组件上下文
 */
export function handleKeyDown(e, context) {
  // 检查是否按下了Delete或Backspace键，并且当前有选中的节点
  if ((e.key === 'Delete' || e.key === 'Backspace') && context.selectedNode) {
    // 避免在输入框中时触发删除节点
    const activeElement = document.activeElement;
    const isInputActive = activeElement && (
      activeElement.tagName === 'INPUT' || 
      activeElement.tagName === 'TEXTAREA' || 
      activeElement.isContentEditable
    );
    
    if (!isInputActive) {
      // 删除当前选中的节点
      context.deleteSelectedNode();
      
      // 阻止默认行为
      e.preventDefault();
    }
  }
}

/**
 * 处理鼠标离开画布事件
 * @param {Object} context - 组件上下文
 */
export function handleMouseLeave(context) {
  context.isDragging = false;
  context.nodeMenu.visible = false;
} 