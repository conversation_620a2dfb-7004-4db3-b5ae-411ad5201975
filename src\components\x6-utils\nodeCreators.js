/**
 * 节点创建相关功能模块
 */
import { Shape } from '@antv/x6';
import { getSvgPathForType } from './nodeUtils';

/**
 * 将内部节点类型映射到目标JSON格式的节点类型
 * @param {String} internalType - 内部节点类型
 * @returns {String} 目标节点类型
 */
function getTargetNodeType(internalType) {
  const typeMapping = {
    'dialogue': 'logicNode',
    'retrieval': 'retrievalNode',
    'generation': 'generateNode',
    'classification': 'categorizeNode',
    'message': 'messageNode',
    'optimization': 'rewriteNode',
    'keywords': 'keywordNode',
    'conditions': 'switchNode',
    'template': 'templateNode',
    'hub': 'logicNode',
    'loop': 'group',
    'code': 'ragNode',
    'comment': 'comment'
  };

  return typeMapping[internalType] || internalType;
}

/**
 * 获取节点类型对应的英文标签
 * @param {String} nodeType - 节点类型
 * @returns {String} 英文标签
 */
function getEnglishLabel(nodeType) {
  const labelMapping = {
    'dialogue': 'Answer',
    'retrieval': 'Retrieval',
    'generation': 'Generate',
    'classification': 'Categorize',
    'message': 'Message',
    'optimization': 'RewriteQuestion',
    'keywords': 'KeywordExtract',
    'conditions': 'Switch',
    'template': 'Template',
    'hub': 'Concentrator',
    'loop': 'Iteration',
    'code': 'Code',
    'comment': 'Note'
  };

  return labelMapping[nodeType] || nodeType;
}

/**
 * 获取节点类型对应的默认中文名称
 * @param {String} nodeType - 节点类型
 * @returns {String} 中文名称
 */
function getDefaultChineseName(nodeType) {
  const nameMapping = {
    'dialogue': '对话_0',
    'retrieval': '知识检索_0',
    'generation': '生成回答_0',
    'classification': '问题分类_0',
    'message': '静态消息_0',
    'optimization': '问题优化_0',
    'keywords': '关键词_0',
    'conditions': '条件_0',
    'template': '模板转换_0',
    'hub': '集线器_0',
    'loop': '循环_0',
    'code': '代码_0',
    'comment': '注释_0'
  };

  return nameMapping[nodeType] || '未命名_0';
}

/**
 * 创建开始节点
 * @param {Object} graph - 图表实例
 * @param {Object} position - 节点位置，可选
 * @param {Object} dslData - DSL中的Begin节点数据，可选
 * @returns {Object} 创建的开始节点
 */
export function createStartNode(graph, position, dslData) {
  // 确保位置数据是有效的数字，处理Vue响应式对象的问题（允许负数坐标）
  const safeX = position && typeof position.x === 'number' && !isNaN(position.x)
    ? position.x
    : Math.round(window.innerWidth / 2) - 250;
  const safeY = position && typeof position.y === 'number' && !isNaN(position.y)
    ? position.y
    : 100;



  const x = safeX;
  const y = safeY;
  const startNode = new Shape.Rect({
    id: 'begin',
    x,
    y,
    width: 250,
    height: 60,
    attrs: {
      body: {
        fill: '#fff',
        stroke: 'transparent',
        strokeWidth: 0,
        rx: 10,
        ry: 10,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 3,
            blur: 10,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      label: {
        text: '开始',
        fill: '#333',
        fontSize: 18,
        fontWeight: '500',
        refX: 0.5,
        refY: 0.5,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      },
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'circle',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
    ],
    ports: {
      groups: {
        out: {
          position: {
            name: 'absolute',
            args: { x: '100%', y: '50%' },
          },
          attrs: {
            portBody: {
              r: 10,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            },
            portText: {
              text: '+',
              fill: '#6366F1',
              fontSize: 15,
              textAnchor: 'middle',
              textVerticalAnchor: 'middle',
              refX: 0.5,
              refY: 0.5,
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
            {
              tagName: 'text',
              selector: 'portText',
            },
          ],
        },
      },
      items: [
        {
          id: 'start-port-out',
          group: 'out',
          args: { x: '100%', y: '50%' },
        },
      ],
    },
    data: {
      type: 'beginNode',
      color: '#6366F1',
      label: 'Begin',
      name: 'begin',
      form: {
        prologue: dslData?.params?.prologue || "Hi! I'm your smart assistant. What can I do for you?"
      },
      // 如果DSL中有output数据，也要保留
      output: dslData?.output || {
        content: {
          "0": {
            content: dslData?.params?.prologue || "Hi! I'm your smart assistant. What can I do for you?"
          }
        }
      }
    },
  });

  // 添加播放图标
  startNode.attr({
    'label': {
      refX: 0.4,
    },
    'icon-bg': {
      r: 12,
      fill: '#6366F1',
      refX: 150,
      refY: '50%',
    },
    'icon': {
      d: 'M 146 25 L 154 30 L 146 35 Z',
      fill: '#ffffff',
      stroke: 'none',
    }
  });

  // 添加到画布
  graph.addNode(startNode);

  // 验证节点数据是否正确设置
  try {
    const addedNode = graph.getCellById('begin');
    if (addedNode) {
      const nodeData = addedNode.getData();
      console.log('[createStartNode] 添加后的节点数据:', nodeData);
      console.log('[createStartNode] 节点form.prologue:', nodeData?.form?.prologue);
      console.log('[createStartNode] 节点output.content:', nodeData?.output?.content);
    } else {
      console.log('[createStartNode] 未找到添加的节点');
    }
  } catch (error) {
    console.log('[createStartNode] 获取节点数据时出错:', error);
    // 直接从startNode获取数据
    const nodeData = startNode.getData();
    console.log('[createStartNode] 从startNode获取的数据:', nodeData);
  }

  return startNode;
}

/**
 * 创建普通节点
 * @param {Object} graph - 图表实例
 * @param {Object} model - 节点模型数据
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @param {Number} nodeIdCounter - 节点ID计数器
 * @returns {Object} 创建的节点
 */
export function createNode(graph, model, x, y) {
  // 用 generateNodeId 自动分配 id
  const allNodes = graph.getNodes().map(n => ({ id: n.id }));
  const nodeId = generateNodeId(model.type, allNodes);
  // 强制覆盖 model.id
  model.id = nodeId;
  
  // 特殊处理注释节点
  if (model.type === 'comment') {
    return createCommentNode(graph, model, nodeId, x, y);
  }
  
  // 特殊处理循环节点
  if (model.type === 'loop') {
    return createLoopNode(graph, model, nodeId, x, y);
  }
  
  // 处理条件节点
  if (model.type === 'conditions') {
    return createConditionNode(graph, model, nodeId, x, y);
  }
  
  // 定义默认高度
  let nodeHeight = 60;
  
  // 为特定节点设置更高的高度
  if (model.type === 'optimization' || model.type === 'keywords' || model.type === 'generation') {
    nodeHeight = 90;
  }
  
  // 问题分类节点需要更高的高度来显示分类列表
  if (model.type === 'classification') {
    // 使用与优化和生成节点一致的高度
    nodeHeight = 90;
    
    // 创建问题分类节点
    const classificationNode = new Shape.Rect({
      id: nodeId,
      x,
      y,
      width: 250,
      height: nodeHeight,
      attrs: {
        body: {
          fill: '#fff',
          stroke: 'transparent',
          strokeWidth: 0,
          rx: 10,
          ry: 10,
          filter: {
            name: 'dropShadow',
            args: {
              dx: 0,
              dy: 3,
              blur: 10,
              color: 'rgba(0,0,0,0.08)',
            },
          },
        },
        label: {
          text: model.name || getDefaultChineseName(model.type),
          fill: '#333',
          fontSize: 18,
          fontWeight: '500',
          refX: 0.55,
          refY: (model.type === 'optimization' || model.type === 'keywords' || model.type === 'generation') ? 0.35 : 0.5,
          textAnchor: 'middle',
          textVerticalAnchor: 'middle',
        },
      },
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
        {
          tagName: 'rect',
          selector: 'icon-bg',
        },
        {
          tagName: 'path',
          selector: 'icon',
        },
        {
          tagName: 'rect',
          selector: 'modelBox',
        },
        {
          tagName: 'text',
          selector: 'modelName',
        },
        {
          tagName: 'text',
          selector: 'categories',
        },
      ],
      ports: {
        groups: {
          in: {
            position: {
              name: 'absolute',
              args: { x: 0, y: 28 }, // 与标题对齐
            },
            attrs: {
              portBody: {
                r: 6,
                magnet: true,
                strokeWidth: 1,
                fill: '#ffffff',
                stroke: '#6366F1'
                // 不要有 visibility: 'hidden'
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'portBody',
              }
            ]
          },
          out: {
            position: {
              name: 'absolute',
              args: { x: '100%', y: '50%' },
            },
            attrs: {
              portBody: {
                r: 6,
                magnet: true,
                strokeWidth: 1,
                fill: '#ffffff',
                stroke: '#F59A23',
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'portBody',
              }
            ]
          }
        },
        items: [
          {
            id: `${nodeId}-port-in`,
            group: 'in',
          }
          // 输出端口将根据分类动态添加
        ]
      },
      data: {
        type: model.type,
        modelType: model.type,
        title: model.name || '问题分类',
        selectedModel: model.selectedModel || '默认模型',
        categories: model.categories || []
      }
    });
    
    // 添加图标和样式，与ModelSelector中保持一致
    const iconColor = '#F59A23'; // 问题分类节点的图标色
    const bgColor = '#FFF7E8'; // 问题分类节点的背景色
    
    classificationNode.attr({
      // 图标背景
      'icon-bg': {
        width: 36,
        height: 36,
        rx: 6,
        ry: 6,
        fill: bgColor,
        stroke: 'transparent',
        strokeWidth: 0,
        refX: 25, // 调整为与X6Graph.vue中一致
        refY: 15, // 调整为与X6Graph.vue中一致
      },
      // 使用SVG路径作为图标
      'icon': {
        d: getSvgPathForType('classification'),
        fill: iconColor,
        refX: 33, // 调整为与X6Graph.vue中一致
        refY: 28, // 调整为与X6Graph.vue中一致
        transform: 'scale(0.7)', // 调整为与X6Graph.vue中一致
      },
      // 添加模型盒子
      'modelBox': {
        x: 20,
        y: nodeHeight - 30,
        width: 250 - 40,
        height: 24,
        rx: 4,
        ry: 4,
        fill: '#f5f5f5', // 添加背景填充
        stroke: '#e0e0e0',
        strokeWidth: 1,
        visibility: 'visible', // 改为默认可见
      },
      'modelName': {
        text: `使用: ${model.selectedModel || '默认模型'}`,
        fill: '#666666',
        fontSize: 13,
        fontWeight: 500,
        refX: 30,
        refY: 0.8,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
        visibility: 'visible', // 改为默认可见
      },
      'categories': {
        text: '',
        fill: '#666',
        fontSize: 12,
        refX: 0.5,
        refY: 0.57,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        visibility: 'hidden',
      }
    });

    // 设置节点数据
    classificationNode.setData({
      modelType: model.type,
      modelId: model.id,
      modelName: model.name,
      selectedModel: model.selectedModel || '默认模型',
      type: getTargetNodeType(model.type),
      label: getEnglishLabel(model.type),
      name: model.name || getDefaultChineseName(model.type),
      form: (() => {
        // 严格按照目标DSL格式设置form数据
        if (model.type === 'classification') {
          // 分类节点也使用空对象，具体数据在设置时添加
          return {};
        } else {
          return model.form || {};
        }
      })()
    });

    // 添加到画布
    graph.addNode(classificationNode);
    
    // 如果有分类数据，立即更新分类显示和连接桩
    if (model.categories && model.categories.length > 0) {
      // 获取对X6Graph.vue实例的引用（可能需要通过事件总线或其他方式实现）
      setTimeout(() => {
        // 使用graph实例上的updateClassificationNode方法
        const updateFn = graph._x6VueWrapper && typeof graph._x6VueWrapper.updateClassificationNode === 'function'
          ? graph._x6VueWrapper.updateClassificationNode
          : null;
          
        if (updateFn) {
          updateFn(classificationNode, model.categories);
        } else if (window.x6VueInstance && typeof window.x6VueInstance.updateClassificationNode === 'function') {
          // 备选方案：使用全局变量
          window.x6VueInstance.updateClassificationNode(classificationNode, model.categories);
        }
      }, 100);
    }
    
    return classificationNode;
  }
  
  // 使用Shape.Rect创建矩形节点
  const node = new Shape.Rect({
    id: nodeId,
    x,
    y,
    width: 250,
    height: nodeHeight,
    type: getTargetNodeType(model.type),
    attrs: {
      body: {
        fill: '#fff',
        stroke: 'transparent',
        strokeWidth: 0,
        rx: 10,
        ry: 10,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 3,
            blur: 10,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      label: {
        text: model.name || getDefaultChineseName(model.type),
        fill: '#333',
        fontSize: 18,
        fontWeight: '500',
        refX: 0.55,
        refY: (model.type === 'optimization' || model.type === 'keywords' || model.type === 'generation' || model.type === 'classification') ? 0.35 : 0.5,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      },
    },
    ports: {
      groups: {
        in: {
          position: {
            name: 'absolute',
            args: { x: 0, y: '50%' },
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
          ],
        },
        out: {
          position: {
            name: 'absolute',
            args: { x: '100%', y: '50%' },
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
          ],
        },
      },
      items: model.type === 'classification' ? [
        {
          id: `${nodeId}-port-in`,
          group: 'in',
        }
      ] : [
        {
          id: `${nodeId}-port-in`,
          group: 'in',
        },
        {
          id: `${nodeId}-port-out`,
          group: 'out',
        },
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'rect',
        selector: 'modelBox',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'rect',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
      {
        tagName: 'text',
        selector: 'modelName',
      },
    ],
    data: {
      modelType: model.type,
      modelId: model.id,
      modelName: model.name,
      selectedModel: model.type === 'optimization' ? '默认大模型' : undefined,
      // 映射节点类型到目标格式（保留在data中用于内部逻辑）
      nodeType: getTargetNodeType(model.type),
      // 设置英文 label
      label: getEnglishLabel(model.type),
      // 设置中文名称
      name: model.name || getDefaultChineseName(model.type),
      // 为特定节点类型添加完整的 form 数据结构
      form: (() => {
        // 严格按照目标DSL格式设置form数据
        switch (model.type) {
          case 'message':
            return {
              messages: []
            };
          case 'conditions':
            console.log('[条件节点创建] 设置form.conditions = []');
            return {
              conditions: []
            };
          case 'keywords':
            return {
              frequencyPenaltyEnabled: false,
              frequency_penalty: 0.7,
              llm_id: model.selectedModel || model.form?.llm_id || "qwen-max@Tongyi-Qianwen",
              maxTokensEnabled: false,
              max_tokens: 256,
              presencePenaltyEnabled: false,
              presence_penalty: 0.4,
              query: [],
              temperature: 0.1,
              temperatureEnabled: false,
              topPEnabled: false,
              top_n: 5,
              top_p: 0.3
            };
          case 'optimization':
            return {
              frequencyPenaltyEnabled: false,
              frequency_penalty: 0.7,
              language: '',
              llm_id: model.selectedModel || model.form?.llm_id || "qwen-max@Tongyi-Qianwen",
              maxTokensEnabled: false,
              max_tokens: 256,
              message_history_window_size: 6,
              presencePenaltyEnabled: false,
              presence_penalty: 0.4,
              temperature: 0.1,
              temperatureEnabled: false,
              topPEnabled: false,
              top_p: 0.3
            };
          case 'generation':
            return {
              cite: true,
              frequencyPenaltyEnabled: false,
              frequency_penalty: 0.7,
              llm_id: model.selectedModel || model.form?.llm_id || "qwen-max@Tongyi-Qianwen",
              maxTokensEnabled: false,
              max_tokens: 256,
              message_history_window_size: 12,
              parameters: [],
              presencePenaltyEnabled: false,
              presence_penalty: 0.4,
              prompt: "请总结以下段落。注意数字，不要胡编乱造。段落如下：\n{input}\n以上就是你需要总结的内容。",
              temperature: 0.1,
              temperatureEnabled: false,
              topPEnabled: false,
              top_p: 0.3
            };
          case 'retrieval':
            return {
              kb_ids: [], // 知识库ID列表，需要用户配置
              keywords_similarity_weight: 0.5, // 提高关键词权重
              query: [],
              similarity_threshold: 0.5, // 提高相似度阈值，减少噪音数据
              top_n: 3, // 减少返回结果数量，避免处理大量数据
              use_kg: false
            };
          case 'code':
            return {
              arguments: [
                { name: "arg1" },
                { name: "arg2" }
              ],
              lang: "python",
              script: "def main(arg1: str, arg2: str) -> str:\n    return f\"result: {arg1 + arg2}\"\n"
            };
          default:
            // 其他所有节点类型都使用空对象
            return {};
        }
      })()
    },
  });

  // 调试输出 type 字段和 model 对象
  // eslint-disable-next-line no-console
  console.log('[节点创建] type:', model.type, 'model:', model);

  // 调试输出节点数据
  if (model.type === 'conditions') {
    console.log('[条件节点] 创建完成，节点数据:', node.getData());
  }
  
  // 添加图标
  // 根据节点类型设置正确的颜色
  let iconColor = model.iconColor;
  let bgColor = model.bgColor;

  // 如果没有设置颜色，根据节点类型使用默认颜色
  if (!iconColor || !bgColor) {
    const colorMapping = {
      'retrieval': { iconColor: '#3370FF', bgColor: '#E9F2FF' },
      'generation': { iconColor: '#A84632', bgColor: '#FFF1F0' },
      'dialogue': { iconColor: '#FA701B', bgColor: '#FFF3E8' },
      'classification': { iconColor: '#F59A23', bgColor: '#FFF7E8' },
      'message': { iconColor: '#00B578', bgColor: '#E8FFF3' },
      'optimization': { iconColor: '#D54BF7', bgColor: '#F5E8FF' },
      'keywords': { iconColor: '#722ED1', bgColor: '#EFE8FF' },
      'conditions': { iconColor: '#8F2CFF', bgColor: '#F0E8FF' },
      'hub': { iconColor: '#13C2C2', bgColor: '#E8FFF8' },
      'template': { iconColor: '#FADB14', bgColor: '#FFFBE8' },
      'loop': { iconColor: '#1890FF', bgColor: '#E8F7FF' },
      'code': { iconColor: '#3491FA', bgColor: '#E8F3FF' },
      'comment': { iconColor: '#FAAD14', bgColor: '#FFFEF7' }
    };

    const colors = colorMapping[model.type] || { iconColor: '#9254de', bgColor: '#efdbff' };
    iconColor = iconColor || colors.iconColor;
    bgColor = bgColor || colors.bgColor;
  }
  
  node.attr({
    // 图标背景
    'icon-bg': {
      width: 36,
      height: 36,
      rx: 6,
      ry: 6, 
      fill: bgColor,
      stroke: 'transparent',
      strokeWidth: 0,
      refX: 25,
      refY: '10%', // 将图标往上移动
    },
    // 使用SVG路径作为图标 - 修复了这里，使用节点自己的类型
    'icon': {
      d: getSvgPathForType(model.type), 
      fill: iconColor,
      refX: 25,
      refY: '10%', // 将图标往上移动
      transform: 'scale(0.8) translate(10, 10)',
    },
  });
  
  // 为特定节点添加底部信息显示
  if (model.type === 'optimization' || model.type === 'keywords' || model.type === 'generation' || model.type === 'classification') {
    let boxText = '使用: 默认大模型';
    
    node.attr({
      'modelBox': {
        x: 20,
        y: nodeHeight - 30,
        width: 250 - 40,
        height: 24,
        rx: 4,
        ry: 4,
        fill: '#f5f5f5',
        stroke: '#e0e0e0',
        strokeWidth: 1,
      },
      'modelName': {
        text: boxText,
        fill: '#666666',
        fontSize: 13,
        fontWeight: 500,
        refX: 45,
        refY: 0.8,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
      }
    });
  }
  
  // 关键词节点展示模型名称
  if (model.type === 'keywords' || model.type === 'keywordNode') {
    // 优先取 model.form.llm_id，其次 model.selectedModel
    let modelName = '默认模型';
    if (model.form && model.form.llm_id) {
      modelName = model.form.llm_id;
    } else if (model.selectedModel) {
      modelName = model.selectedModel;
    }
    node.attr({
      'modelBox': {
        x: 20,
        y: nodeHeight - 30,
        width: 250 - 40,
        height: 24,
        rx: 4,
        ry: 4,
        fill: '#f5f5f5',
        stroke: '#e0e0e0',
        strokeWidth: 1,
      },
      'modelName': {
        text: `使用: ${modelName}`,
        fill: '#666666',
        fontSize: 13,
        fontWeight: 500,
        refX: 45,
        refY: 0.8,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
        visibility: 'visible',
      }
    });
  }
  
  // 添加模型名称（仅针对特定类型）
  if (model.type === 'optimization' || model.type === 'keywords' || model.type === 'generation' || model.type === 'classification') {
    node.attr({
      modelName: {
        text: '使用: 默认模型',
        fill: '#666',
        fontSize: 14,
        refX: 0.5,
        refY: 0.8,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      }
    });
  }
  
  // 为问题分类节点添加分类列表显示
  if (model.type === 'classification') {
    // 确保markup中包含所有需要的元素
    node.markup = [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'rect',
        selector: 'modelBox',
      },
      {
        tagName: 'rect',
        selector: 'categoryBox',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'rect',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
      {
        tagName: 'text',
        selector: 'modelName',
      },
      {
        tagName: 'text',
        selector: 'categories',
      },
    ];
    
    // 设置初始图标颜色
    node.attr({
      'icon-bg': {
        fill: '#FFF7E8', // 问题分类节点的背景色
      },
      'icon': {
        fill: '#F59A23', // 问题分类节点的图标色
      },
      modelName: {
        text: `使用: ${model.selectedModel || '默认模型'}`,
        fill: '#666666',
        fontSize: 13,
        fontWeight: 500,
        refX: 45,
        refY: 0.8,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
        visibility: 'visible', // 始终显示模型名称
      },
      categories: {
        text: '',
        fill: '#666666',
        fontSize: 13,
        fontWeight: 500,
        refX: 45,
        refY: 0.9,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
      },
      categoryBox: {
        x: 20,
        y: nodeHeight - 30,
        width: 0, // 初始宽度为0，不显示
        height: 0, // 初始高度为0，不显示
        rx: 4,
        ry: 4,
        fill: '#f5f5f5',
        stroke: '#e0e0e0',
        strokeWidth: 1,
        visibility: 'hidden', // 初始隐藏
      }
    });
    
    // 问题分类节点默认没有出口连接桩
    // 分类将在后续动态添加连接桩
  }
  
  // 设置节点类型（在data中，供getGraphData使用）
  node.setData({
    ...node.getData(),
    type: model.type  // 保存原始类型
  });

  // 添加节点到画布
  graph.addNode(node);

  return node;
}

/**
 * 创建注释节点
 * @param {Object} graph - 图表实例
 * @param {Object} model - 节点模型数据
 * @param {String} id - 节点ID
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的注释节点
 */
export function createCommentNode(graph, model, id, x, y) {
  // 1. 生成内容HTML
  function measureHtmlHeight(html, width = 226) {
    const div = document.createElement('div');
    // 使用与实际渲染完全相同的样式
    div.style.cssText = `position:absolute;visibility:hidden;width:${width - 16}px;font-size:14px;line-height:1.4;white-space:pre-line;word-break:break-all;padding:4px 0 0 0;margin:0;box-sizing:border-box;`;
    div.innerHTML = html || '双击编辑注释内容...';
    document.body.appendChild(div);
    const height = div.offsetHeight;
    document.body.removeChild(div);
    return Math.max(height, 50); // 确保最小高度能显示一行文字
  }
  const minContentHeight = 60; // 增加最小内容区高度，确保有足够显示空间
  const maxContentHeight = 200; // 最大内容区高度（超出出现滚动条）

  // 计算实际内容高度，增加一些额外空间
  const actualContentHeight = measureHtmlHeight(model.content || '双击编辑注释内容...') + 10;
  const contentHeight = Math.min(Math.max(actualContentHeight, minContentHeight), maxContentHeight);

  // 3. 设定节点和 foreignObject 高度 - 精确计算，最小化空白
  const headerHeight = 36; // 标题区高度
  const nodeHeight = headerHeight + contentHeight; // 去除额外padding，紧凑布局

  const node = new Shape.Rect({
    id,
    x,
    y,
    width: 250,
    height: nodeHeight,
    attrs: {
      body: {
        fill: model.bgColor || '#FFFEF7', // 使用正确的背景色
        stroke: '#F5DD98',
        strokeWidth: 1,
        rx: 8,
        ry: 8,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 2,
            blur: 8,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      header: {
        fill: '#FFF8DC',
        stroke: '#F5DD98',
        strokeWidth: 1,
        height: 36,
        width: 250,
        y: 0,
        rx: 8,
        ry: 8,
      },
      label: {
        text: model.name || getDefaultChineseName(model.type),
        fill: '#333',
        fontSize: 16,
        fontWeight: '500',
        refX: 45,
        refY: 18,
        textAnchor: 'start',
        textVerticalAnchor: 'middle',
      },
      // 图标背景
      'icon-bg': {
        cx: 25,
        cy: 18,
        r: 12,
        fill: model.bgColor || '#FFFEF7',
        stroke: 'transparent',
        strokeWidth: 0,
      },
      // 图标
      icon: {
        d: getSvgPathForType(model.type),
        fill: model.iconColor || '#FAAD14',
        refX: 25,
        refY: 18,
        refWidth: 16,
        refHeight: 16,
        transform: 'translate(-8, -8)',
      },
      // foreignObject 内容区 - 支持内联编辑
      content: {
        x: 12,
        y: 35, // 稍微向上移动，紧贴header底部
        width: 226,
        height: contentHeight,
        html: `<div xmlns="http://www.w3.org/1999/xhtml"
                    class="comment-content-wrapper"
                    style="font-size:14px;color:#666;word-break:break-all;white-space:pre-line;line-height:1.4;padding:0 8px 4px 8px;position:relative;border-radius:0 0 8px 8px;height:100%;overflow:hidden;margin:0;box-sizing:border-box;">
          <div class="comment-content-editable"
               contenteditable="false"
               data-placeholder="双击编辑注释内容..."
               style="outline:none;height:${contentHeight - 4}px;cursor:pointer;overflow-y:auto;max-height:${maxContentHeight - 4}px;margin:0;padding:4px 0 0 0;box-sizing:border-box;"
               data-node-id="${id}">
            ${model.content || ''}
          </div>
          ${!model.content || model.content.trim() === '' ? '<div class="comment-hint" style="position:absolute;bottom:2px;right:6px;font-size:11px;color:#999;opacity:0.7;pointer-events:none;">💡 双击编辑</div>' : ''}
        </div>`
      },
    },
    markup: [
      { tagName: 'rect', selector: 'body' },
      { tagName: 'rect', selector: 'header' },
      { tagName: 'circle', selector: 'icon-bg' },
      { tagName: 'path', selector: 'icon' },
      { tagName: 'text', selector: 'label' },
      // foreignObject 替代 text
      { tagName: 'foreignObject', selector: 'content' },
    ],
    // 移除ports属性，注释节点不需要连接桩
    data: {
      modelType: model.type,
      modelId: model.id,
      modelName: model.name || '注释',
      content: model.content || '',
      isComment: true,
      // 添加标准字段以符合目标格式
      label: getEnglishLabel(model.type),
      name: getDefaultChineseName(model.type),
      form: {
        text: model.content || ''
      }
    },
    // 设置节点类型
    type: 'noteNode',
  });
  
  // 设置节点类型（在data中，供getGraphData使用）
  node.setData({
    ...node.getData(),
    type: model.type  // 保存原始类型 'comment'
  });

  // 添加节点到画布
  graph.addNode(node);

  // 设置节点的自定义属性，用于CSS选择器
  const nodeView = graph.findViewByCell(node);
  if (nodeView && nodeView.container) {
    nodeView.container.setAttribute('data-shape', 'comment');
  }
  
  return node;
}

/**
 * 创建循环节点
 * @param {Object} graph - 图表实例
 * @param {Object} model - 节点模型数据
 * @param {String} id - 节点ID
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的循环节点
 */
export function createLoopNode(graph, model, id, x, y) {
  // 导入循环节点创建函数
  const { createLoopNode } = require('./loopUtils');
  return createLoopNode(graph, model, id, x, y);
} 

/**
 * 创建条件节点
 * @param {Object} graph - 图表实例
 * @param {Object} model - 节点模型数据
 * @param {String} id - 节点ID
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的条件节点
 */
export function createConditionNode(graph, model, id, x, y) {
  const node = new Shape.Rect({
    id,
    x,
    y,
    width: 250,
    height: 100, // 增加高度至100px
    attrs: {
      body: {
        fill: '#fff',
        stroke: 'transparent', // 确保边框是透明的
        strokeWidth: 0, // 确保边框宽度为0 - 避免紫色边框
        rx: 10,
        ry: 10,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 3,
            blur: 10,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      label: {
        text: model.name || getDefaultChineseName(model.type),
        fill: '#333',
        fontSize: 18,
        fontWeight: '500',
        refX: 0.55,
        refY: 0.3, // 调整位置，将标题放在上部
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      },
      // 添加Else分支信息 - 放在右下角
      elseLabel: {
        text: 'ELSE: ' + (model.elseAction ? getActionLabel(model.elseAction) : '未设置'),
        fill: '#666',
        fontSize: 12,
        fontWeight: '400',
        ref: 'elseBoxEmpty',
        refX: 0.5,
        refY: 0.5,
        textAnchor: 'end', // 右对齐
        textVerticalAnchor: 'middle',
      },
    },
    ports: {
      groups: {
        // 输入端口组
        in: {
          position: {
            name: 'left',
            args: { dy: -10 }, // 左侧连接桩与标题同高
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1', // 使用紫色
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
          ],
        },
        // 条件分支端口组
        case: {
          position: {
            name: 'right',
            args: { dy: 0 }, // 动态计算位置
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
          ],
        },
        // else分支端口组
        else: {
          position: {
            name: 'right',
            args: { dy: 20 }, // 动态计算位置
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
          ],
        },
      },
      items: [
        {
          id: `${id}-port-in`,
          group: 'in',
        },
        // 不预创建输出端口，在updateConditionNode中动态创建
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'rect',
        selector: 'elseBoxEmpty',
      },
      {
        tagName: 'text',
        selector: 'elseLabelEmpty',
      },
      {
        tagName: 'rect',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
      // 新增：分支信息文本
      ...((model.form && Array.isArray(model.form.conditions)) ? model.form.conditions.map((c, idx) => ({
        tagName: 'text',
        selector: `branchInfo-${idx}`
      })) : [])
    ],
    data: {
      type: 'switchNode',
      modelType: model.type,
      modelId: model.id,
      modelName: model.name || '条件',
      label: 'Switch',
      name: model.name || '条件_0',
      id: model.id || '',
      elseAction: model.elseAction || '',
      cases: model.cases ? JSON.parse(JSON.stringify(model.cases)) : [],
      // 添加form字段，确保有conditions数组
      form: {
        conditions: model.form?.conditions || [],
        end_cpn_id: model.elseAction || ''
      }
    },
  });
  
  // 添加图标 - 使用ModelSelector中的颜色
  const iconColor = '#8F2CFF'; // 条件节点的图标色，与ModelSelector一致
  const bgColor = '#F0E8FF';   // 条件节点的背景色，与ModelSelector一致
  
  // 设置分支信息文本
  if (model.form && Array.isArray(model.form.conditions)) {
    model.form.conditions.forEach((c, idx) => {
      const condText = (Array.isArray(c.items) && c.items.length > 0)
        ? c.items.map(item => `${item.cpn_id || ''} ${item.operator || ''} ${item.value || ''}`).join(' 且 ')
        : '';
      const toText = c.to ? `→ ${c.to}` : '';
      node.attr({
        [`branchInfo-${idx}`]: {
          text: `分支${idx + 1}: ${condText} ${toText}`,
          fontSize: 12,
          fill: '#8F2CFF',
          refX: 0.5,
          refY: 100 + 18 * idx, // 100为节点底部，依次向下排列
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
        }
      });
    });
  }
  
  node.attr({
    // 图标背景
    'icon-bg': {
      width: 36,
      height: 36,
      rx: 6,
      ry: 6, 
      fill: bgColor,
      stroke: 'transparent',
      strokeWidth: 0,
      refX: 25,
      refY: '10%', // 将图标往上移动
    },
    // 使用SVG路径作为图标
    'icon': {
      d: getSvgPathForType('conditions'),
      fill: iconColor,
      refX: 25,
      refY: '10%', // 将图标往上移动
      transform: 'scale(0.8) translate(10, 10)',
    },
    // 设置ELSE框
    'elseBoxEmpty': {
      refX: 20,
      refY: 60,
      width: 210,
      height: 30,
      rx: 4,
      ry: 4,
      fill: '#FFF7E8', // ELSE使用橙色背景
      stroke: '#F59A23',
      strokeWidth: 1,
    },
    // 更新默认ELSE标签
    'elseLabelEmpty': {
      text: 'ELSE: ' + (model.elseAction ? getActionLabel(model.elseAction) : '未设置'),
      fill: '#F59A23',
      fontSize: 12,
      fontWeight: 500,
      refX: 0.5,
      refY: 75,
      textAnchor: 'middle',
      textVerticalAnchor: 'middle',
    },
  });
  
  // 在节点添加到画布后，手动设置data-shape属性
  graph.addNode(node);
  
  // 确保菜单按钮元素有正确的data-shape属性
  setTimeout(() => {
    const nodeView = graph.findViewByCell(node);
    if (nodeView) {
      const menuButtonEl = nodeView.findOne('text[selector="menuButton"]');
      if (menuButtonEl && menuButtonEl.node) {
        menuButtonEl.node.setAttribute('data-shape', 'menuButton');
      }
    }
    
    // 初始化条件节点的显示和连接桩
    const updateFn = graph._x6VueWrapper && typeof graph._x6VueWrapper.updateConditionNode === 'function'
      ? graph._x6VueWrapper.updateConditionNode
      : null;
      
    if (updateFn) {
      const cases = model.cases ? JSON.parse(JSON.stringify(model.cases)) : [];
      const elseAction = model.elseAction || '';
      updateFn(node, cases, elseAction);
    } else if (window.x6VueInstance && typeof window.x6VueInstance.updateConditionNode === 'function') {
      // 备选方案：使用全局变量
      const cases = model.cases ? JSON.parse(JSON.stringify(model.cases)) : [];
      const elseAction = model.elseAction || '';
      window.x6VueInstance.updateConditionNode(node, cases, elseAction);
    }
  }, 0);
  
  return node;
}

/**
 * 根据操作类型获取显示标签
 * @param {String} action - 操作类型
 * @returns {String} 显示标签
 */
function getActionLabel(action) {
  const actionLabels = {
    'retrieval': '知识检索',
    'generation': '生成回答',
    'dialogue': '对话',
    'message': '静态消息',
    'optimization': '问题优化',
    'keywords': '关键词',
    'conditions': '条件',
    'hub': '集线器',
    'template': '模板转换',
    'loop': '循环',
    'code': '代码'
  };
  
  return actionLabels[action] || action;
} 

// 类型到前缀映射表 - 更新为目标JSON格式
const typeToPrefix = {
  // 内部类型映射
  'dialogue': 'Answer',
  'retrieval': 'Retrieval',
  'generation': 'Generate',
  'classification': 'Categorize',
  'message': 'Message',
  'optimization': 'RewriteQuestion',
  'keywords': 'KeywordExtract',
  'conditions': 'Switch',
  'template': 'Template',
  'hub': 'Concentrator',
  'loop': 'Iteration',
  'code': 'Code',
  'comment': 'Note',

  // 目标类型映射
  'logicNode': 'Answer',
  'retrievalNode': 'Retrieval',
  'generateNode': 'Generate',
  'categorizeNode': 'Categorize',
  'messageNode': 'Message',
  'rewriteNode': 'RewriteQuestion',
  'keywordNode': 'KeywordExtract',
  'switchNode': 'Switch',
  'templateNode': 'Template',
  'group': 'Iteration',
  'ragNode': 'Code',
  'iterationStartNode': 'IterationItem',
  'noteNode': 'Note'
};

function generateNodeId(type, allNodes) {
  const prefix = typeToPrefix[type] || type;
  const sameTypeNodes = allNodes.filter(n => n.id && n.id.startsWith(prefix + '_'));
  let maxIndex = -1;
  sameTypeNodes.forEach(n => {
    const match = n.id.match(new RegExp(`^${prefix}_(\\d+)$`));
    if (match) {
      const idx = parseInt(match[1], 10);
      if (idx > maxIndex) maxIndex = idx;
    }
  });
  return `${prefix}_${maxIndex + 1}`;
} 