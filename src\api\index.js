// src/api/index.js
import axios from 'axios';

// 开发环境使用代理，生产环境使用实际地址
export const RAGFLOW_BASE_URL = 'http://140.143.132.222:8000'; // 生产环境使用实际地址
export const RAGFLOW_API_KEY = 'ragflow-c5ZDA5MmJhNjJlNjExZjA5ZWE4NzZjMD';

const ragflowRequest = axios.create({
  baseURL: RAGFLOW_BASE_URL,
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${RAGFLOW_API_KEY}`
  }
});

export default ragflowRequest;