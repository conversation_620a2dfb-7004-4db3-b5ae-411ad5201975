<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词Query为空问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .step {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 10px;
            margin: 10px 0;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>关键词Query为空问题调试指南</h1>
        <p>这个页面帮助你调试为什么关键词组件的query仍然为空。</p>

        <div class="debug-section">
            <div class="debug-title">🔍 问题分析</div>
            <p>你的关键词组件数据显示query为空数组：</p>
            <div class="code">"query": []</div>
            
            <div class="warning">
                <strong>⚠️ 可能的原因：</strong>
                <ul>
                    <li>用户还没有在设置界面中选择查询输入源</li>
                    <li>组件列表没有正确加载</li>
                    <li>数据保存时出现问题</li>
                    <li>组件初始化时机问题</li>
                </ul>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🛠️ 调试步骤</div>
            
            <div class="step">
                <strong>步骤1：检查控制台日志</strong>
                <p>打开浏览器开发者工具(F12)，查看Console标签页，寻找以下日志：</p>
                <div class="code">
[KeywordsSettings] 接收到initialData: {...}
[KeywordsSettings] newVal.form?.query: []
[KeywordsSettings] 初始化query，当前query: []
[KeywordsSettings] query为空，selectedQueryComponentId设置为空
[KeywordsSettings] 开始加载可用组件...
[KeywordsSettings] 提取到的可用组件: [...]
                </div>
            </div>

            <div class="step">
                <strong>步骤2：检查关键词节点设置界面</strong>
                <p>1. 打开关键词节点设置</p>
                <p>2. 查看是否有"查询输入源"下拉框</p>
                <p>3. 检查下拉框是否有选项</p>
                <p>4. 如果没有选项，点击刷新按钮</p>
            </div>

            <div class="step">
                <strong>步骤3：手动选择查询输入源</strong>
                <p>1. 在"查询输入源"下拉框中选择一个组件（如：Answer_0）</p>
                <p>2. 查看控制台是否有以下日志：</p>
                <div class="code">
[KeywordsSettings] selectedQueryComponentId变化: Answer_0
[KeywordsSettings] 更新query为: [{component_id: "Answer_0", type: "reference"}]
                </div>
                <p>3. 保存设置</p>
            </div>

            <div class="step">
                <strong>步骤4：验证数据保存</strong>
                <p>保存后，重新查看DSL数据，query应该变为：</p>
                <div class="code">
"query": [
  {
    "component_id": "Answer_0",
    "type": "reference"
  }
]
                </div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🔧 快速修复方案</div>
            
            <div class="success">
                <strong>✅ 方案1：手动设置（推荐）</strong>
                <ol>
                    <li>打开关键词节点设置</li>
                    <li>在"查询输入源"下拉框中选择上游组件</li>
                    <li>保存设置</li>
                </ol>
            </div>

            <div class="warning">
                <strong>⚠️ 方案2：如果下拉框为空</strong>
                <ol>
                    <li>点击下拉框右侧的刷新按钮</li>
                    <li>等待组件列表加载</li>
                    <li>选择合适的组件</li>
                    <li>保存设置</li>
                </ol>
            </div>

            <div class="error">
                <strong>❌ 方案3：如果刷新后仍为空</strong>
                <p>这可能是图表数据获取问题，请检查：</p>
                <ul>
                    <li>图表中是否有其他节点</li>
                    <li>节点是否正确连接</li>
                    <li>控制台是否有错误信息</li>
                </ul>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📋 预期的数据流</div>
            <div class="code">
1. 用户打开关键词节点设置
   ↓
2. KeywordsSettings组件加载
   ↓
3. 获取图表数据，提取可用组件列表
   ↓
4. 显示"查询输入源"下拉框
   ↓
5. 用户选择组件（如：Answer_0）
   ↓
6. selectedQueryComponentId = "Answer_0"
   ↓
7. 触发watch，更新nodeData.form.query
   ↓
8. query = [{component_id: "Answer_0", type: "reference"}]
   ↓
9. 用户保存设置
   ↓
10. 数据保存到DSL中
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🎯 下一步操作</div>
            <div class="step">
                <strong>立即操作：</strong>
                <ol>
                    <li>打开关键词节点设置</li>
                    <li>查看控制台日志</li>
                    <li>检查"查询输入源"下拉框</li>
                    <li>选择一个组件并保存</li>
                    <li>验证DSL数据是否更新</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
