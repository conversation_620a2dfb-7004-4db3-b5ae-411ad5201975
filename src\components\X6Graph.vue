<template>
  <div class="x6-graph-container" @dragover="handleDragOver" @drop="handleDrop" @mouseleave="handleMouseLeave">
    <div class="agent-title-bar">{{ agentName || '未选择' }}</div>
    <div ref="container" class="graph-container"></div>
  </div>
</template>

<script>
import { Graph } from '@antv/x6';

// 导入模块化的功能
import { createGraphConfig } from './x6-utils/graphConfig';
import { getConnectingConfig } from './x6-utils/connectionValidators';
import { selectNode, clearSelection, getSvgPathForType, parseCategoriesFromDSL, buildPortHandleMap } from './x6-utils/nodeUtils';
import { createStartNode, createNode } from './x6-utils/nodeCreators';
import { setupLoopSyncMovement } from './x6-utils/loopUtils';
import { duplicateNode, deleteNode, isNodeInsideLoop, isIterationItem } from './x6-utils/menuHandlers';
import { handleMenuMouseEnter, handleMenuMouseLeave } from './x6-utils/menuHandlers';
import { initGraphEvents, handleDragOver as dragOverHandler, handleDrop as dropHandler, handleGlobalClick, handleKeyDown, handleMouseLeave as mouseLeaveHandler } from './x6-utils/eventHandlers';
// import { createEdge as createCustomEdge } from './x6-utils/connectionValidators'; // 暂时不使用



export default {
  name: 'X6Graph',
  props: {
    agentId: {
      type: String,
      default: null
    },
    agentName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      graph: null,
      nodeIdCounter: 1,
      nodeMenu: {
        visible: false,
        currentNodeId: null,
        top: 0,
        left: 0
      },
      isMouseOverMenu: false,
      menuCloseTimeout: null,
      selectedNode: null,
      isDragging: false,
      currentDraggingNodeId: null,
      currentAgentId: this.agentId,
      // 🗑️ 已删除：本地对话历史管理
      // 现在使用Agent API的session管理

      // 🔧 新增：内联编辑状态管理
      inlineEditingNodeId: null,  // 当前正在内联编辑的节点ID
      originalContent: '',        // 编辑前的原始内容
      isInlineEditing: false      // 是否正在内联编辑
    };
  },
  computed: {
    // 判断当前菜单节点是否在循环内部
    isMenuNodeInsideLoop() {
      if (!this.nodeMenu.currentNodeId || !this.graph) return false;
      
      const node = this.graph.getCellById(this.nodeMenu.currentNodeId);
      if (!node) return false;
      
      return isNodeInsideLoop(node.getData());
    },
    isIterationItemNode() {
      if (!this.nodeMenu.currentNodeId || !this.graph) return false;
      
      const node = this.graph.getCellById(this.nodeMenu.currentNodeId);
      if (!node) return false;
      
      return isIterationItem(node.getData());
    }
  },
  watch: {
    agentId(newVal) {
      this.currentAgentId = newVal;
    },
    selectedNode(newVal) {
      if (newVal) {
        this.$emit('node-selected', {
          id: newVal.id,
          type: newVal.type,
          data: newVal.getData()
        });
      } else {
        this.$emit('node-selected', null);
      }
    }
  },
  mounted() {
    // 初始化会话ID
    this.sessionId = this.generateSessionId();
    console.log('[X6Graph] 初始化会话ID:', this.sessionId);

    this.initGraph();
    window.addEventListener('resize', this.resizeGraph);
    document.addEventListener('click', this.handleGlobalClick);
    document.addEventListener('mousemove', this.handleGlobalMouseMove);
    // 添加键盘删除键事件监听（全局）
    document.addEventListener('keydown', this.handleKeyDown);

    // 添加全局引用，使nodeCreators能够访问updateClassificationNode
    window.x6VueInstance = this;

    // 为Graph实例添加对Vue组件的引用
    if (this.graph) {
      this.graph._x6VueWrapper = this;
    }
  },
  
  beforeDestroy() {
    // 🔧 清理内联编辑状态
    if (this.isInlineEditing) {
      this.endInlineEdit(false); // 不保存，直接取消
    }

    if (this.graph) {
      this.graph.dispose();
      this.graph._x6VueWrapper = null;
    }
    window.removeEventListener('resize', this.resizeGraph);
    document.removeEventListener('click', this.handleGlobalClick);
    document.removeEventListener('mousemove', this.handleGlobalMouseMove);
    document.removeEventListener('keydown', this.handleKeyDown);

    if (this.menuCloseTimeout) {
      clearTimeout(this.menuCloseTimeout);
      this.menuCloseTimeout = null;
    }

    // 清理全局引用
    if (window.x6VueInstance === this) {
      window.x6VueInstance = null;
    }
  },
  methods: {
    // ==================== 对话历史管理方法 ====================

    /**
     * 生成会话ID
     * @returns {string} 会话ID
     */
    generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    // 🗑️ 已删除：本地对话历史管理功能
    // 现在使用Agent API的session管理，不需要本地历史管理

    // 🗑️ 已删除：getCurrentConversationForNodes方法

    // 🗑️ 已删除：saveConversationToDSL方法

    // 🗑️ 已删除：getSessionStats方法
    // 现在使用Agent API管理会话，不需要本地统计

    // 🗑️ 已删除：getCompleteDataForSave方法

    // 🗑️ 已删除：prepareSaveData方法

    // ==================== 图表初始化和基础方法 ====================

    initGraph() {
      // 创建画布配置
      const config = createGraphConfig(this.$refs.container);
      
      // 添加连接配置
      config.connecting = getConnectingConfig();
      
      // 创建画布
      this.graph = new Graph(config);

      // 添加初始的"开始"节点
      createStartNode(this.graph);
      
      // 设置循环节点和内容区域的同步移动
      setupLoopSyncMovement(this.graph);
      
      // 初始化图表事件
      initGraphEvents(this.graph, this);

      // 删除按钮现在通过createEdgeWithTools直接添加到边上，一直显示
      // 不再需要鼠标事件来动态添加/移除工具
    },
    
    // 选择节点
    selectNode(node) {
      selectNode(this.graph, node);
    },
    
    // 清除选择
    clearSelection() {
      clearSelection(this.graph);
    },
    
    // 调整图表大小
    resizeGraph() {
      if (this.graph) {
        this.graph.resize(
          this.$refs.container.clientWidth,
          this.$refs.container.clientHeight
        );
      }
    },
    
    // 处理全局点击事件，用于关闭菜单
    handleGlobalClick(e) {
      handleGlobalClick(e, this);
    },
    
    // 处理全局鼠标移动事件
    handleGlobalMouseMove() {
      // 此处可以添加全局鼠标移动处理逻辑
    },
    
    // 处理键盘事件
    handleKeyDown(e) {
      handleKeyDown(e, this);
    },
    
    // 菜单相关处理方法
    handleMenuMouseEnter() {
      handleMenuMouseEnter(this.nodeMenu, () => {
        if (this.menuCloseTimeout) {
          clearTimeout(this.menuCloseTimeout);
          this.menuCloseTimeout = null;
        }
      });
    },
    
    handleMenuMouseLeave() {
      handleMenuMouseLeave(this.nodeMenu, (callback) => {
        this.menuCloseTimeout = setTimeout(callback, 150);
      }, () => {
      this.nodeMenu.visible = false;
      });
    },

    // 处理鼠标离开画布事件
    handleMouseLeave() {
      mouseLeaveHandler(this);
    },
    
    // 复制当前节点
    duplicateCurrentNode() {
      duplicateNode(this.graph, this.nodeMenu.currentNodeId, (model, x, y) => {
        this.createNode(model, x, y);
      });
      
      // 隐藏菜单
      this.nodeMenu.visible = false;
    },
    
    // 删除当前节点
    deleteCurrentNode() {
      deleteNode(this.graph, this.nodeMenu.currentNodeId, this.$message);
      
      // 隐藏菜单
      this.nodeMenu.visible = false;
    },

    // 删除当前选中的节点
    deleteSelectedNode() {
      if (!this.selectedNode) return;
      
      deleteNode(this.graph, this.selectedNode.id, this.$message);
      
      // 清除选中状态
      this.selectedNode = null;
    },
    
    // 处理拖拽经过事件
    handleDragOver(event) {
      dragOverHandler(event);
    },
    
    // 处理拖拽放置事件
    handleDrop(event) {
      dropHandler(event, this, (model, x, y) => {
        this.createNode(model, x, y);
      });
    },
    
    // 根据模型类型创建节点
    createNode(model, x, y) {

      return createNode(this.graph, model, x, y, this.nodeIdCounter++);
    },

    // 🔧 新增：更新注释节点的显示和数据（弹窗编辑用）
    updateCommentNode(node, title, content) {
      if (!node) return;

      const nodeData = node.getData() || {};

      // 更新节点数据
      const updatedData = {
        ...nodeData,
        content: content,
        form: {
          ...nodeData.form,
          text: content
        }
      };
      node.setData(updatedData);

      // 更新节点显示 - 标题
      node.attr('label/text', title);

      // 更新内容显示 - 使用新的内联编辑结构
      const maxContentHeight = 200;

      // 重新计算内容高度
      function measureContentHeight(text) {
        const div = document.createElement('div');
        div.style.cssText = `position:absolute;visibility:hidden;width:210px;font-size:14px;line-height:1.4;white-space:pre-line;word-break:break-all;padding:4px 0 0 0;margin:0;box-sizing:border-box;`;
        div.innerHTML = text || '双击编辑注释内容...';
        document.body.appendChild(div);
        const height = div.offsetHeight;
        document.body.removeChild(div);
        return Math.max(height, 60); // 增加最小高度
      }

      const calculatedHeight = measureContentHeight(content) + 10; // 增加额外空间
      const finalHeight = Math.min(calculatedHeight, maxContentHeight);

      const contentHtml = `<div xmlns="http://www.w3.org/1999/xhtml"
                              class="comment-content-wrapper"
                              style="font-size:14px;color:#666;word-break:break-all;white-space:pre-line;line-height:1.4;padding:0 8px 4px 8px;position:relative;border-radius:0 0 8px 8px;height:100%;overflow:hidden;margin:0;box-sizing:border-box;">
        <div class="comment-content-editable"
             contenteditable="false"
             data-placeholder="双击编辑注释内容..."
             style="outline:none;height:${finalHeight - 4}px;cursor:pointer;overflow-y:auto;max-height:${maxContentHeight - 4}px;margin:0;padding:4px 0 0 0;box-sizing:border-box;"
             data-node-id="${node.id}">
          ${content || ''}
        </div>
        ${!content || content.trim() === '' ? '<div class="comment-hint" style="position:absolute;bottom:2px;right:6px;font-size:11px;color:#999;opacity:0.7;pointer-events:none;">💡 双击编辑</div>' : ''}
      </div>`;

      // 更新content属性
      node.attr('content/html', contentHtml);

      // 🔧 动态调整节点高度
      const newNodeHeight = 36 + finalHeight; // header高度 + 内容高度
      node.resize(250, newNodeHeight);

      // 🔧 强制刷新节点视图
      this.$nextTick(() => {
        try {
          // 获取节点视图
          const view = this.graph.findViewByCell(node);
          if (view) {
            // 强制重新渲染
            view.update();
          }
        } catch (error) {
          console.warn('[注释更新] 视图更新失败:', error);
        }
      });

      console.log('[注释更新] 节点更新完成:', { title, content, nodeId: node.id });
    },

    // 🔧 新增：开始内联编辑注释节点
    startInlineEdit(node) {
      console.log('[内联编辑] startInlineEdit被调用，node:', node);

      if (!node) {
        console.error('[内联编辑] node参数为空');
        return;
      }

      const nodeData = node.getData() || {};
      console.log('[内联编辑] 节点数据:', nodeData);

      // 只处理注释节点
      if (nodeData.type !== 'comment') {
        console.log('[内联编辑] 不是注释节点，跳过');
        return;
      }

      console.log('[内联编辑] 开始编辑注释节点:', node.id);

      // 如果已经有其他节点在编辑，先结束编辑
      if (this.isInlineEditing && this.inlineEditingNodeId !== node.id) {
        console.log('[内联编辑] 结束其他节点的编辑');
        this.endInlineEdit(false); // 不保存
      }

      // 设置编辑状态
      this.isInlineEditing = true;
      this.inlineEditingNodeId = node.id;
      this.originalContent = nodeData.content || '';
      console.log('[内联编辑] 编辑状态设置完成');

      // 获取节点视图
      const view = this.graph.findViewByCell(node);
      if (!view) {
        console.error('[内联编辑] 未找到节点视图');
        return;
      }
      console.log('[内联编辑] 找到节点视图');

      // 查找contenteditable元素
      const editableElement = view.container.querySelector('.comment-content-editable');
      if (!editableElement) {
        console.error('[内联编辑] 未找到可编辑元素');
        console.log('[内联编辑] 容器内容:', view.container.innerHTML);
        return;
      }

      console.log('[内联编辑] 找到可编辑元素:', editableElement);

      // 启用编辑
      editableElement.contentEditable = 'true';
      editableElement.style.border = '2px solid #409EFF';
      editableElement.style.backgroundColor = '#f8f9fa';
      editableElement.style.cursor = 'text';

      console.log('[内联编辑] 编辑样式设置完成');

      // 隐藏提示
      const hintElement = view.container.querySelector('.comment-hint');
      if (hintElement) {
        hintElement.style.display = 'none';
        console.log('[内联编辑] 隐藏提示元素');
      }

      // 立即聚焦，简化流程
      try {
        editableElement.focus();
        console.log('[内联编辑] 聚焦成功');

        // 选中所有文本
        if (editableElement.textContent) {
          const range = document.createRange();
          range.selectNodeContents(editableElement);
          const selection = window.getSelection();
          selection.removeAllRanges();
          selection.addRange(range);
          console.log('[内联编辑] 文本选中完成');
        }

        // 简化事件监听器添加
        this.addInlineEditListeners(editableElement, node);

      } catch (error) {
        console.error('[内联编辑] 聚焦或选中文本失败:', error);
      }
    },

    // 🔧 新增：添加内联编辑事件监听器
    addInlineEditListeners(editableElement, node) {
      console.log('[内联编辑] 添加事件监听器');

      // 失去焦点时保存 - 简化处理
      const blurHandler = () => {
        console.log('[内联编辑] blur事件触发');
        if (this.isInlineEditing && this.inlineEditingNodeId === node.id) {
          this.endInlineEdit(true, node);
        }
      };

      // 键盘事件处理
      const keydownHandler = (e) => {
        if (e.key === 'Escape') {
          e.preventDefault();
          this.endInlineEdit(false, node); // 取消编辑
        } else if (e.key === 'Enter' && e.ctrlKey) {
          e.preventDefault();
          this.endInlineEdit(true, node); // 保存编辑
        }
      };

      // 阻止事件冒泡和默认行为
      const stopPropagation = (e) => {
        e.stopPropagation();
        e.preventDefault();
      };

      // 防止双击事件干扰
      const preventDoubleClick = (e) => {
        e.stopPropagation();
        e.preventDefault();
      };

      // 添加事件监听器
      editableElement.addEventListener('blur', blurHandler);
      editableElement.addEventListener('keydown', keydownHandler);
      editableElement.addEventListener('click', stopPropagation);
      editableElement.addEventListener('mousedown', stopPropagation);
      editableElement.addEventListener('dblclick', preventDoubleClick);

      console.log('[内联编辑] 事件监听器添加完成');

      // 保存事件监听器引用，用于后续移除
      editableElement._inlineEditListeners = {
        blur: blurHandler,
        keydown: keydownHandler,
        click: stopPropagation,
        mousedown: stopPropagation,
        dblclick: preventDoubleClick
      };
    },

    // 🔧 新增：结束内联编辑
    endInlineEdit(save = true, node = null) {
      if (!this.isInlineEditing) return;

      // 获取节点
      if (!node && this.inlineEditingNodeId) {
        node = this.graph.getCellById(this.inlineEditingNodeId);
      }

      if (!node) {
        this.resetInlineEditState();
        return;
      }

      const view = this.graph.findViewByCell(node);
      if (!view) {
        this.resetInlineEditState();
        return;
      }

      const editableElement = view.container.querySelector('.comment-content-editable');
      if (!editableElement) {
        this.resetInlineEditState();
        return;
      }

      // 获取编辑后的内容
      const newContent = editableElement.textContent || '';

      // 移除事件监听器
      if (editableElement._inlineEditListeners) {
        const listeners = editableElement._inlineEditListeners;
        editableElement.removeEventListener('blur', listeners.blur);
        editableElement.removeEventListener('keydown', listeners.keydown);
        editableElement.removeEventListener('click', listeners.click);
        editableElement.removeEventListener('mousedown', listeners.mousedown);
        editableElement.removeEventListener('dblclick', listeners.dblclick);
        delete editableElement._inlineEditListeners;
      }

      // 禁用编辑
      editableElement.contentEditable = 'false';
      editableElement.style.border = 'none';
      editableElement.style.backgroundColor = 'transparent';
      editableElement.style.cursor = 'pointer';

      // 显示提示（如果内容为空）
      const hintElement = view.container.querySelector('.comment-hint');
      if (hintElement) {
        hintElement.style.display = newContent.trim() ? 'none' : 'block';
      }

      // 保存或恢复内容
      if (save && newContent !== this.originalContent) {
        console.log('[内联编辑] 保存内容:', {
          nodeId: node.id,
          oldContent: this.originalContent,
          newContent
        });

        // 使用现有的更新方法
        this.updateCommentNodeContent(node, newContent);
      } else if (!save) {
        console.log('[内联编辑] 取消编辑，恢复原内容');

        // 恢复原内容
        editableElement.textContent = this.originalContent;
      }

      // 重置状态
      this.resetInlineEditState();
    },

    // 🔧 新增：重置内联编辑状态
    resetInlineEditState() {
      this.isInlineEditing = false;
      this.inlineEditingNodeId = null;
      this.originalContent = '';
    },

    // 🔧 测试方法：手动触发内联编辑
    testInlineEdit() {
      console.log('[测试] 查找注释节点进行测试');
      const nodes = this.graph.getNodes();
      const commentNode = nodes.find(node => {
        const data = node.getData();
        return data && data.type === 'comment';
      });

      if (commentNode) {
        console.log('[测试] 找到注释节点，开始测试内联编辑');
        this.startInlineEdit(commentNode);
      } else {
        console.log('[测试] 未找到注释节点');
      }
    },

    // 🔧 新增：更新注释节点内容（内联编辑专用）
    updateCommentNodeContent(node, content) {
      if (!node) return;

      const nodeData = node.getData() || {};

      // 更新节点数据
      const updatedData = {
        ...nodeData,
        content: content,
        form: {
          ...nodeData.form,
          text: content
        }
      };
      node.setData(updatedData);

      console.log('[内联编辑] 节点内容更新完成:', { nodeId: node.id, content });
    },

    // 🔧 新增：直接编辑注释节点（用于双击事件 - 已废弃，使用内联编辑）
    editCommentNode(node) {
      if (!node) return;

      const nodeData = node.getData() || {};

      // 只处理注释节点
      if (nodeData.type !== 'comment') return;

      console.log('[直接编辑] 使用内联编辑代替抽屉编辑:', node.id);

      // 🔧 直接调用内联编辑，不再触发抽屉
      this.startInlineEdit(node);
    },

    // 编辑当前节点
    editCurrentNode() {
      const nodeId = this.nodeMenu.currentNodeId;
      if (!nodeId) return;
      
      const node = this.graph.getCellById(nodeId);
      if (!node) return;
      
      // 隐藏菜单
      this.nodeMenu.visible = false;
      
      const nodeData = node.getData() || {};
      
      // 根据节点类型触发不同的编辑事件
      if (nodeData.type === 'comment') {
        // 🔧 注释节点使用内联编辑，不再触发抽屉
        console.log('[菜单编辑] 注释节点使用内联编辑:', nodeId);
        this.startInlineEdit(node);
        return;
      } else if (nodeData.type === 'loop') {
        this.$emit('edit-loop', {
          id: nodeId,
          title: node.attr('label/text') || '循环',
          content: nodeData.content || '',
          onSave: (title, content) => {
            // 更新节点数据
            node.setData({
              ...nodeData,
              content: content
            });
            
            // 更新节点显示
            node.attr({
              'label/text': title,
            });
          }
        });
      } else if (nodeData.type === 'iteration-item') {
        this.$emit('edit-iteration-item', {
          id: nodeId,
          title: node.attr('label/text') || 'IterationItem',
          settings: nodeData.settings || {},
          onSave: (title, settings) => {
            // 更新节点数据
            node.setData({
              ...nodeData,
              title: title,
              settings: settings
            });
            
            // 更新节点显示
            node.attr({
              'label/text': title,
            });
          }
        });
      } else if (nodeData.type === 'classification') {
        this.$emit('edit-classification', {
          id: nodeId,
          title: node.attr('label/text') || '问题分类',
          categories: nodeData.categories || [],
          onSave: (title, categories) => {
            // 更新节点数据
            node.setData({
              ...nodeData,
              title: title,
              categories: categories
            });
            
            // 更新节点显示
            node.attr({
              'label/text': title,
            });
            
            // 处理分类显示和连接桩
            this.updateClassificationNode(node, categories);
          }
        });
      } else if (nodeData.type === 'template') {
        this.$emit('edit-template', {
          id: nodeId,
          title: node.attr('label/text') || '模板转换',
          description: nodeData.description || '',
          content: nodeData.content || '',
          onSave: (title, description, content) => {
            // 更新节点数据
            node.setData({
              ...nodeData,
              title: title,
              description: description,
              content: content
            });
            
            // 更新节点显示
            node.attr({
              'label/text': title,
            });
          }
        });
      } else if (nodeData.type === 'conditions') {
        // 确保使用非响应式的 cases 数组
        const casesDeepCopy = nodeData.cases ? JSON.parse(JSON.stringify(nodeData.cases)) : [];
        
        this.$emit('edit-condition', {
          id: nodeId,
          title: node.attr('label/text') || '条件',
          elseAction: nodeData.elseAction || '',
          cases: casesDeepCopy,
          onSave: (id, elseAction, cases, title) => {
            // 确保使用非响应式的 cases 数组
            const savedCasesDeepCopy = cases ? JSON.parse(JSON.stringify(cases)) : [];
            
            // 创建全新的节点数据对象
            const newData = {
              type: 'conditions',
              modelType: nodeData.modelType,
              modelId: nodeData.modelId,
              modelName: title || nodeData.modelName || '条件',
              id: id || nodeData.id,
              title: title || '条件',
              elseAction: elseAction,
              cases: savedCasesDeepCopy
            };
            
            // 强制重置节点数据
            node.data = null;
            
            // 设置新数据（完全替换而不是合并）
            node.setData(newData);
            
            // 更新节点显示
            node.attr({
              'label/text': title || '条件',
              'elseLabel/text': 'ELSE: ' + (elseAction ? this.getActionLabel(elseAction) : '未设置')
            });
          }
        });
      }
    },
    
    // 更新问题分类节点的分类和连接桩
    updateClassificationNode(node, categories) {
      if (!node) return;



      // 保存现有连接的边信息
      const connectedEdges = this.graph.getConnectedEdges(node);
      const edgeConnections = [];

      connectedEdges.forEach(edge => {
        const source = edge.getSource();
        const target = edge.getTarget();

        if (source.cell === node.id && source.port && source.port.startsWith('category-port-')) {
          // 这是从分类端口出去的边
          edgeConnections.push({
            type: 'outgoing',
            portId: source.port,
            targetCell: target.cell,
            targetPort: target.port,
            edge: edge
          });
        } else if (target.cell === node.id && target.port && target.port.endsWith('-port-in')) {
          // 这是进入分类节点输入端口的边
          edgeConnections.push({
            type: 'incoming',
            portId: target.port,
            sourceCell: source.cell,
            sourcePort: source.port,
            edge: edge
          });
        }
      });



      // 只移除 category-port-x，不移除 in 端口
      const ports = node.getPorts() || [];
      ports.forEach(port => {
        if (port.id.startsWith('category-port-')) {
          node.removePort(port.id);
        }
      });
      
      // 获取节点数据
      const nodeData = node.getData() || {};
      const selectedModel = nodeData.selectedModel || '默认模型';
      
      // 获取节点宽度，用于计算位置
      const nodeWidth = node.size().width;
      
      // 确保分类节点的颜色与ModelSelector中一致，并固定标题和图标在顶部
      node.attr({
        'body': {
          stroke: 'transparent', // 去掉橙色边框
          strokeWidth: 1,
          fill: '#FFFFFF',
        },
        'icon-bg': {
          fill: '#FFF7E8', // 问题分类节点的背景色
          refX: 25, // 图标背景位置 - 调整为与图标匹配
          refY: 10 ,
          width: 36,
          height: 36,
          rx: 6,
          ry: 6,
        },
        'icon': {
          fill: '#F59A23', // 问题分类节点的图标色
          d: 'M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z', // 使用ModelSelector中的文档图标
          refX: 33, // 图标位置 - 水平居中于背景
          refY: 20,
          transform: 'scale(0.7)', // 调整图标大小
        },
        'label': {
          text: nodeData.title || '问题分类',
          refX: 100,
          refY: 28, // 固定在顶部区域
          fontSize: 16,
          fontWeight: 400,
          textAnchor: 'start',
          textVerticalAnchor: 'middle',
        }
        // 不再设置 menuButton
      });
      
      // 确保节点有正确的端口组配置
      const nodeConfig = node.getProp('ports') || {};
      nodeConfig.groups = nodeConfig.groups || {};
      console.log('设置的端口的分组',nodeConfig.groups);
      
      // 配置输入端口组
      if (!nodeConfig.groups.in) {
        nodeConfig.groups.in = {
          position: {
            name: 'absolute',
            args: { x: 0, y: 28 },
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            }
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            }
          ]
        };
      }
      
      // 配置输出端口组 - 但不再使用这个组，而是为每个分类单独配置端口
      nodeConfig.groups.out = {
          position: {
            name: 'absolute',
            args: { x: '100%', y: '50%' },
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1',
            }
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            }
          ]
        };

      // 先移除所有旧的分类连接桩，确保重新创建时没有残留
      const allPorts = node.getPorts() || [];
      allPorts.forEach(port => {
        if (port.id.startsWith('category-port-')) {
          node.removePort(port.id);
        }
      });
      
      // 设置节点的端口配置
      node.setProp('ports', nodeConfig);
      
      // 定义基础布局参数
      const titleHeight = 50; // 标题区域高度
      const modelBoxHeight = 28; // 模型名称区域高度 - 统一为28px
      const categoryHeight = 28; // 每个分类的高度 - 统一为28px
      const categoryGap = 10; // 分类之间的间距 - 减小间距
      const modelToCategoryGap = 12; // 模型框与第一个分类框之间的间距 - 减小间距
      const categoryStartY = titleHeight + modelBoxHeight + modelToCategoryGap; // 分类起始位置
      const categoryBoxWidth = nodeWidth - 40; // 分类框宽度（左右各留20px的边距）
      
      // 为每个分类添加独立的右侧端口
      categories.forEach((cat, index) => {
        const yPosition = categoryStartY + (index * (categoryHeight + categoryGap)) + categoryHeight / 2;
        const portId = `category-port-${index}`;
        node.addPort({
          id: portId,
          group: 'out',
          args: { x: nodeWidth, y: yPosition },
          // 🔥 将分类名称存储在端口数据中，方便后续获取
          categoryName: cat.name,  // 直接存储分类名称
          categoryIndex: index     // 直接存储分类索引
        });
      });
      
      // 添加输入端口 - 位于左侧与标题同高度
        const inPort = ports.find(port => port.id === `${node.id}-port-in`);
        if (inPort) {
          node.removePort(inPort.id);
        }
        
        node.addPort({
          id: `${node.id}-port-in`,
          group: 'in',
          position: {
            name: 'absolute',
            args: { x: 0, y: 28 }  // 与标题Y坐标相同
          },
          attrs: {
            circle: {
              r: 6,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1' // 使用灰色而不是紫色
            }
          }
        });
      
      // 显示大模型名称 - 固定在标题区域下方
      node.attr({
        'modelBox': {
          x: 20, // 使用绝对坐标确保位置固定
          y: titleHeight + 5, // 从顶部向下的固定位置
          width: categoryBoxWidth, // 使用与分类框相同的宽度
          height: modelBoxHeight, // 使用统一高度
          rx: 4,
          ry: 4,
          fill: '#f5f5f5',
          stroke: '#e0e0e0',
          strokeWidth: 1,
          visibility: 'visible'
        }
      });
      
      // 单独设置模型名称文本，确保它正确显示在模型框内
      node.attr({
        'modelName': {
          text: `使用: ${selectedModel}`,
          fill: '#666666',
          fontSize: 13,
          fontWeight: 400,
          ref: 'modelBox', // 引用modelBox作为父元素
          refX: 0.5, // 水平居中于modelBox
          refY: 0.5, // 垂直居中于modelBox
          textAnchor: 'middle', // 文本居中对齐
          textVerticalAnchor: 'middle',
          visibility: 'visible'
        }
      });
      
      // 完全重置节点的markup，而不是修改现有的
      // 构建基本元素
      let newMarkup = [
        // 基本节点元素
        { tagName: 'rect', selector: 'body' },
        { tagName: 'text', selector: 'label' },
        { tagName: 'rect', selector: 'icon-bg' },
        { tagName: 'path', selector: 'icon' },
        { tagName: 'rect', selector: 'modelBox' },
        { tagName: 'text', selector: 'modelName' }
      ];
      
      // 如果没有分类，只显示基本节点和模型信息
      if (!categories || categories.length === 0) {
        // 调整节点高度 - 只需要标题和模型区域
        const totalHeight = titleHeight + modelBoxHeight + 10; // 加一些底部边距
        node.resize(nodeWidth, totalHeight);
        node.setMarkup(newMarkup);
        return;
      }
      
      // 计算节点总高度 - 确保足够的空间
      const totalCategoriesHeight = (categories.length * (categoryHeight + categoryGap));
      const bottomPadding = 20; // 增加底部内边距
      const totalHeight = categoryStartY + totalCategoriesHeight + bottomPadding;
      
      // 调整节点高度
      node.resize(nodeWidth, totalHeight);
      
      // 为每个分类添加元素 - 使用唯一的选择器名称
      categories.forEach((cat, index) => {
        // 添加分类框元素 - 确保选择器名称唯一
        newMarkup.push({
          tagName: 'rect',
          selector: `cat-box-${index}`, // 修改选择器名称格式
        });
        
        // 添加分类文本元素 - 确保选择器名称唯一
        newMarkup.push({
          tagName: 'text',
          selector: `cat-text-${index}`, // 修改选择器名称格式
        });
        
        // 添加连接桩指示器元素 - 确保选择器名称唯一
        newMarkup.push({
          tagName: 'circle',
          selector: `cat-port-indicator-${index}`, // 修改选择器名称格式
        });
        
        // 添加连接桩图标元素 - 确保连接桩可见
        newMarkup.push({
          tagName: 'text',
          selector: `cat-port-icon-${index}`,
        });
      });
      
              // 设置新的markup
      node.setMarkup(newMarkup);
      
              // 恢复连接桩的视觉指示器
      for (let index = 0; index < categories.length; index++) {
        const yPosition = categoryStartY + (index * (categoryHeight + categoryGap));
        // 添加可见的连接桩指示器 - 精确放置在节点右边缘
        node.attr({
          [`cat-port-indicator-${index}`]: {
            cx: nodeWidth, // 节点右边缘
            cy: yPosition + (categoryHeight / 2), // 分类框的中心位置
            r: 6,
            fill: '#ffffff',
            stroke: '#6366F1', // 使用灰色
            strokeWidth: 1,
            opacity: 1,
            visibility: 'visible'
          }
        });
      }
      
      // 设置每个分类的属性
      categories.forEach((cat, index) => {
        // 获取分类名称
        const categoryName = cat && typeof cat === 'object' && cat.name !== undefined 
          ? cat.name 
          : `分类${index + 1}`;
        
        // 计算垂直位置 - 确保足够的间距
        const yPosition = categoryStartY + (index * (categoryHeight + categoryGap));
        
        // 设置分类框属性 - 使用模型框的样式
        node.attr({
          [`cat-box-${index}`]: {
            refX: 20,
            refY: yPosition, // 相对于节点的Y位置
            width: categoryBoxWidth,
            height: categoryHeight,
            rx: 4,
            ry: 4,
            fill: '#f5f5f5', // 与模型框相同的填充色
            stroke: '#e0e0e0', // 与模型框相同的边框色
            strokeWidth: 1,
            pointerEvents: 'none', // 防止遮挡连接桩
          }
        });
        
        // 设置分类文本属性 - 使用相对定位
        node.attr({
          [`cat-text-${index}`]: {
            text: categoryName, // 移除序号，使文本更简洁
            ref: `cat-box-${index}`, // 引用对应的分类框
            refX: 0.5, // 水平居中
            refY: 0.5, // 垂直居中
            fontSize: 14,
            fontWeight: 500,
            fill: '#666666',
            textAnchor: 'middle', // 文本居中对齐
            textVerticalAnchor: 'middle',
            cursor: 'pointer',
          }
        });
        
        //           // 确保先移除任何同ID的旧端口
        // const portId = `category-port-${index}`;
        //   const existingPort = node.getPort(portId);
        //   if (existingPort) {
        //     node.removePort(portId);
        //   }
          
        //   // 这里不使用端口组，而是为每个分类单独配置端口
        //   try {
        //     // 计算精确的端口位置 - 确保端口在节点边缘
        //     const nodeSize = node.size();
        //     const nodePos = node.position();
        //     const absoluteY = yPosition + (categoryHeight / 2); // 分类中心高度的绝对位置
        //     const relativeY = absoluteY - nodePos.y; // 转换为相对于节点的Y坐标
            
        //     // 计算分类连接桩位置
            
        //     // 添加连接桩 - 确保每个连接桩位于不同位置
        //   node.addPort({
        //       id: portId, // 使用唯一ID
        //       // 使用右侧边缘锚点，精确定位在节点边缘
        //     position: {
        //         name: 'right',  // 使用X6内置的右侧定位，确保在边缘
        //       args: {
        //           // dy是相对于右侧中心点的偏移量
        //           dy: relativeY - (nodeSize.height / 2)
        //       }
        //     },
        //     attrs: {
        //         portBody: {
        //           r: 6, // 与左侧一致的尺寸
        //           magnet: true,
        //           strokeWidth: 1, // 与左侧一致
        //           fill: '#ffffff',
        //           stroke: '#6366F1', // 使用灰色
        //           visibility: 'visible' // 确保可见
        //         },
        //       },
        //                       // 使用与左侧连接桩相同的标记结构
        //     markup: [
        //       {
        //         tagName: 'circle',
        //         selector: 'portBody'
        //       }
        //     ]
        //   });
            
        // 创建分类连接桩
        // } catch (error) {
        //   console.error(`添加连接桩失败: ${portId}`, error);
        // }
      });

      // 重新连接之前保存的边
      setTimeout(() => {
        edgeConnections.forEach(connection => {
          if (connection.type === 'outgoing') {
            // 根据端口ID找到对应的分类索引
            const portIndex = parseInt(connection.portId.replace('category-port-', ''));

            // 检查新的分类中是否还有这个索引
            if (portIndex < categories.length) {
              const newPortId = `category-port-${portIndex}`;

              // 检查新端口是否存在
              const newPort = node.getPort(newPortId);
              if (newPort) {
                console.log('[updateClassificationNode] 重新连接输出边:', {
                  from: `${node.id}:${newPortId}`,
                  to: `${connection.targetCell}:${connection.targetPort}`
                });

                // 创建新的边
                const edge = this.createEdgeWithTools();

                edge.setSource({ cell: node.id, port: newPortId });
                edge.setTarget({ cell: connection.targetCell, port: connection.targetPort });
                this.graph.addEdge(edge);
              }
            }
          } else if (connection.type === 'incoming') {

            // 重新连接输入端口的边
            const newPortId = `${node.id}-port-in`;

            // 检查输入端口是否存在
            const newPort = node.getPort(newPortId);
            if (newPort) {
              console.log('[updateClassificationNode] 重新连接输入边:', {
                from: `${connection.sourceCell}:${connection.sourcePort}`,
                to: `${node.id}:${newPortId}`
              });

              // 创建新的边
              const edge = this.createEdgeWithTools();

              edge.setSource({ cell: connection.sourceCell, port: connection.sourcePort });
              edge.setTarget({ cell: node.id, port: newPortId });
              this.graph.addEdge(edge);
            }
          }
        });
      }, 100);
    },

    // === 条件节点端口创建方法 ===

    // 创建Case端口（统一方法）- 带位置参数版本
    createCasePort(node, portId, yPosition, index) {
      try {
        // 计算端口位置 - 与If标签使用相同的定位方式
        const nodeSize = node.size();



        // 使用与分类组件完全相同的方法：使用out端口组 + args坐标
        node.addPort({
          id: portId,
          group: 'out', // 使用与分类组件相同的端口组
          args: {
            x: nodeSize.width, // 端口在右边缘（与分类组件一致）
            y: yPosition + 5   // 与If标签完全相同的Y位置
          }
        });





        // 端口创建完成

        return portId;
      } catch (e) {
        console.error(`[createCasePort] 创建case端口 ${index} 失败:`, e);
        return null;
      }
    },

    // 创建Else端口（统一方法）- 带位置参数版本
    createElsePort(node, elseYPosition) {
      try {
        const elsePortId = `else-port-${Date.now()}`;
        const nodeSize = node.size(); // 获取节点尺寸



        // 使用与分类组件完全相同的方法：使用out端口组 + args坐标
        node.addPort({
          id: elsePortId,
          group: 'out', // 使用与分类组件相同的端口组
          args: {
            x: nodeSize.width, // 端口在右边缘（与分类组件一致）
            y: elseYPosition   // 使用传入的else位置
          }
        });

        // 创建后立即设置精确位置
        const absolutePosition = {
          name: 'absolute',
          args: {
            x: nodeSize.width - 6, // 端口在右边缘
            y: elseYPosition // 使用传入的else位置
          }
        };

        node.setPortProp(elsePortId, 'position', absolutePosition);



        // 保存else端口ID到节点数据
        node.setData({
          ...node.getData(),
          elsePortId: elsePortId
        });



        // else端口创建完成

        return elsePortId;
      } catch (e) {
        console.error('[createElsePort] 创建else端口失败:', e);
        return null;
      }
    },



    // 更新条件节点的分支和连接桩
    updateConditionNode(node, cases, elseAction) {
      if (!node) return;

      // 保存现有连接的边信息
      const connectedEdges = this.graph.getConnectedEdges(node);
      const edgeConnections = [];

      connectedEdges.forEach(edge => {
        const source = edge.getSource();
        const target = edge.getTarget();

        if (source.cell === node.id && source.port && (source.port.startsWith('case-port-') || source.port.startsWith('else-port-'))) {
          // 这是从条件端口出去的边
          edgeConnections.push({
            type: 'outgoing',
            portId: source.port,
            targetCell: target.cell,
            targetPort: target.port,
            edge: edge
          });
        } else if (target.cell === node.id && target.port && target.port.endsWith('-port-in')) {
          // 这是进入条件节点输入端口的边
          edgeConnections.push({
            type: 'incoming',
            portId: target.port,
            sourceCell: source.cell,
            sourcePort: source.port,
            edge: edge
          });
        }
      });

      console.log('[updateConditionNode] 保存的连接:', edgeConnections);

      // 自动兼容 cases 和 form.conditions
      let realCases = cases;
      if ((!realCases || realCases.length === 0) && node.getData() && node.getData().form && Array.isArray(node.getData().form.conditions)) {
        realCases = node.getData().form.conditions.map(item => ({
          ...item,
          conditions: Array.isArray(item.items) ? item.items : (item.conditions || [])
        }));
      }
      // 1. 移除所有旧的分支信息文本
      const oldMarkup = node.getMarkup().filter(m => !(m.selector && /^branchInfo-\d+$/.test(m.selector)));
      node.setMarkup(oldMarkup);
      // 2. 重新添加分支信息文本到markup
      const branchTexts = (realCases || []).map((c, idx) => ({
        tagName: 'text',
        selector: `branchInfo-${idx}`
      }));
      node.setMarkup([...oldMarkup, ...branchTexts]);
      // 3. 设置每个分支的文本内容
      (realCases || []).forEach((c, idx) => {
        // 只取第一个条件用于方框内主显示
        const mainCond = (Array.isArray(c.conditions) && c.conditions.length > 0) ? c.conditions[0] : {};
        console.log('mainCond:', mainCond); // 调试输出
        let cpn = mainCond.cpn_id || '';
        const op = mainCond.operator || mainCond.comparator || '=';
        const val = mainCond.value !== undefined ? mainCond.value : '';
        // 省略超长cpn_id
        const maxCpnLen = 10;
        const cpnDisplay = cpn.length > maxCpnLen ? cpn.slice(0, maxCpnLen) + '...' : cpn;
        node.attr({
          [`branchInfo-${idx}`]: {
            text: `${cpnDisplay} ${op} ${val}`.trim(),
            fontSize: 10,
            fill: '#8F2CFF',
            refX: 0.5,
            refY: 100 + 18 * idx,
            textAnchor: 'middle',
            textVerticalAnchor: 'top',
          }
        });
      });
      
      // === 端口清理和重建 ===
      console.log('[updateConditionNode] 开始端口重建流程');

      // 1. 获取当前所有端口
      const currentPorts = node.getPorts() || [];
      console.log('[updateConditionNode] 当前端口:', currentPorts.map(p => ({ id: p.id, group: p.group })));

      // 2. 移除所有输出端口，保留输入端口
      currentPorts.forEach(port => {
        if (port.id.includes('case-port-') || port.id.includes('else-port-')) {
          console.log('[updateConditionNode] 移除输出端口:', port.id);
          node.removePort(port.id);
        }
      });

      // 3. 验证端口清理结果
      const remainingPorts = node.getPorts() || [];
      console.log('[updateConditionNode] 清理后剩余端口:', remainingPorts.map(p => p.id));
      
      // 获取节点数据
      const nodeData = node.getData() || {};
      
      // 获取节点宽度，用于计算位置
      const nodeWidth = node.size().width;
      console.log('[updateConditionNode] 开始位置计算，nodeWidth:', nodeWidth);
      
      // 确保条件节点的颜色与ModelSelector中一致
      node.attr({
        'body': {
          stroke: 'transparent', // 移除边框颜色
          strokeWidth: 0, // 将边框宽度设为0
          fill: '#FFFFFF',
        },
        'icon-bg': {
          fill: '#F0E8FF', // 条件节点的背景色
          refX: 25,
          refY: 10,
          width: 36,
          height: 36,
          rx: 6,
          ry: 6,
        },
        'icon': {
          fill: '#8F2CFF', // 条件节点的图标色
          d: getSvgPathForType('conditions'), // 使用条件节点的图标
          refX: 33,
          refY: 20,
          transform: 'scale(0.7)',
        },
        'label': {
          text: nodeData.title || '条件',
          refX: 100,
          refY: 28, // 固定在顶部区域
          fontSize: 16,
          fontWeight: 400,
          textAnchor: 'start',
          textVerticalAnchor: 'middle',
        },
        'menuButton': {
          text: '⋮',
          fill: '#ccc',
          fontSize: 20,
          fontWeight: 'bold',
          refX: '95%',
          refY: '10%',
          textAnchor: 'middle',
          cursor: 'pointer',
        }
      });
      
      // 确保节点有正确的端口组配置
      const nodeConfig = node.getProp('ports') || {};
      nodeConfig.groups = nodeConfig.groups || {};
      
      // 配置输入端口组
      if (!nodeConfig.groups.in) {
        nodeConfig.groups.in = {
          position: {
            name: 'left',  // 使用左侧锚点
            args: { dy: 0 },  // 相对于左侧中心点
          },
          attrs: {
            portBody: {
              r: 6,
              magnet: true, // 关键
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#6366F1' // 使用灰色
            }
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            }
          ]
        };
      }
      
      // 配置输出端口组 - 但不再使用这个组，而是为每个分支单独配置端口
      nodeConfig.groups.out = {
        position: { name: 'absolute' },
        attrs: {
          portBody: {
            r: 6,
            magnet: true, // 关键
            strokeWidth: 1,
            fill: '#ffffff',
            stroke: '#6366F1'
          }
        },
        markup: [
          { tagName: 'circle', selector: 'portBody' }
        ]
      };
      
      // 先移除所有旧的分支连接桩，确保重新创建时没有残留
      const allPorts = node.getPorts() || [];
      allPorts.forEach(port => {
        if (port.id.startsWith('case-port-')) {
          node.removePort(port.id);
        }
      });
      
      // 设置节点的端口配置
      node.setProp('ports', nodeConfig);
      
      // 定义基础布局参数
      const titleHeight = 40; // 标题区域高度
      const caseHeight = 30; // 每个分支的高度
      const caseGap = 8; // 分支之间的间距
      const conditionGap = 8; // 条件之间的间距
      const caseStartY = titleHeight + 10; // 分支起始位置
      const caseBoxWidth = nodeWidth - 40; // 分支框宽度（左右各留20px的边距）
      const elseBoxHeight = 30; // ELSE分支区域的高度
      const elseBoxMargin = 15; // ELSE分支与最后一个case之间的距离
      const bottomPadding = 20; // 节点底部内边距
      
      // 预先初始化节点高度变量
      let nodeHeight;

      // 获取当前节点的所有端口
      const ports = node.getPorts() || [];

      // 添加输入端口 - 位于左侧与标题同高度
      const inPort = ports.find(port => port.id === `${node.id}-port-in`);
      if (inPort) {
        node.removePort(inPort.id);
      }
      
      node.addPort({
        id: `${node.id}-port-in`,
        group: 'in',
        position: {
          name: 'left',  // 使用左侧锚点
          args: {
            dy: -10 // 固定偏移量，与标题高度对齐
          }
        },
        attrs: {
          circle: {
            r: 6,
            magnet: true,
            strokeWidth: 1,
            fill: '#ffffff',
            stroke: '#6366F1'
          }
        }
      });
      
      // 完全重置节点的markup，而不是修改现有的
      // 构建基本元素
      let newMarkup = [
        // 基本节点元素
        { tagName: 'rect', selector: 'body' },
        { tagName: 'text', selector: 'label' },
        { tagName: 'rect', selector: 'icon-bg' },
        { tagName: 'path', selector: 'icon' },
        { tagName: 'text', selector: 'menuButton' },
        { tagName: 'text', selector: 'elseLabelWithCases' }, // 添加ELSE文本到基本元素中
        { tagName: 'circle', selector: 'else-port-indicator' } // 添加ELSE连接桩指示器
      ];
      
      // 如果没有分支，只显示基本节点和ELSE信息
      if (!realCases || realCases.length === 0) {
        // 只添加ELSE文本元素，不再添加盒子
        newMarkup.push({ tagName: 'text', selector: 'elseLabelEmpty' });
        
        // 调整节点高度 - 只需要标题和ELSE区域
        const totalHeight = titleHeight + elseBoxHeight + 20; // 加一些底部边距
        node.resize(nodeWidth, totalHeight);
        
        // 显示ELSE分支 - 无框，灰色字体
        node.attr({
          'elseLabelEmpty': {
            text: `ELSE: ${elseAction ? this.getActionLabel(elseAction) : '未设置'}`,
            refX: 0.6,
            refY: 0.8,
            fontSize: 14,
            fontWeight: 500,
            fill: '#666666', // 使用灰色
            textAnchor: 'start',
            textVerticalAnchor: 'middle',
          }
        });

        // 为没有分支的情况也创建else端口
        const elseRefY = 0.8; // 与文本位置一致
        const elseYPosition = elseRefY * totalHeight;

        console.log('[updateConditionNode] 无分支情况 - Else端口位置计算:', {
          totalHeight,
          elseRefY,
          elseYPosition,
          nodeWidth
        });

        // === 创建初始Else端口（无case条件时）===
        const elsePortId = this.createElsePort(node, elseYPosition);

        node.setMarkup(newMarkup);

        // 验证端口创建结果
        setTimeout(() => {
          console.log('[updateConditionNode] 验证端口创建结果:', {
            nodeSize: node.size(),
            elsePortExists: !!node.getPort(elsePortId),
            allPorts: node.getPorts().map(p => ({ id: p.id, group: p.group }))
          });
        }, 100);

        return;
      }
      
      // 计算节点总高度 - 考虑所有分支的条件数量
      nodeHeight = caseStartY;
      
      // 保存每个分支的垂直位置，用于后续设置
      const branchPositions = [];
      let currentPosition = caseStartY;
      
      realCases.forEach((caseItem, idx) => {
        // 保存当前分支的开始位置
        branchPositions.push(currentPosition);
        
        const conditions = caseItem.conditions || [];
        const branchHeight = conditions.length > 0
          ? conditions.length * caseHeight + (conditions.length - 1) * conditionGap
          : caseHeight;
          
        // 更新当前位置，添加分支高度
        currentPosition += branchHeight;
        
        // 为下一个分支增加间距 - 调整为更均衡的间距
        if (idx === 0) {
          // If与第一个ElseIf之间增大间距
          currentPosition += caseGap * 4; 
        } else {
          // ElseIf与ElseIf之间减小间距
          currentPosition += caseGap * 4; 
        }
      });
      
      // 总高度等于最后一个位置加上ELSE分支的高度和底部边距
      nodeHeight = currentPosition + elseBoxHeight + elseBoxMargin + bottomPadding;
      
      // 调整节点高度
      node.resize(nodeWidth, nodeHeight);
      
      // 设置新的markup
      node.setMarkup(newMarkup);
      
      // 为每个分支准备连接桩的位置信息
      const casePositions = [];
      
      // 移除case端口指示器的markup，只使用真正的端口
      
      // 使用之前计算的位置设置每个分支
      realCases.forEach((caseItem, index) => {
        const yPosition = branchPositions[index]; // 使用预先计算的位置
        
        // 移除视觉指示器，只使用真正的端口
        
        const firstCondY = yPosition + 15; // 第一个条件的Y位置
        
        casePositions.push({
          index,
          yPosition,
          firstCondY,
        });
      });
      
      // 设置每个分支的属性
      casePositions.forEach(posInfo => {
        const index = posInfo.index;
        const yPosition = posInfo.yPosition;
        const firstCondY = posInfo.firstCondY;
        
        // 获取当前case的条件
        const caseItem = realCases[index];
        // 条件列表
        const conditions = caseItem.conditions || [];
        
        // 统一使用灰色背景框，不再需要区分颜色
        
        // 添加分支标签元素(If/ElseIf)
        newMarkup.push({
          tagName: 'text',
          selector: `case-label-${index}`
        });
        
        // 为整个Case添加一个大框
        newMarkup.push({
          tagName: 'rect',
          selector: `case-box-${index}`
        });
        
        // 设置Case框
        const caseBoxHeight = conditions.length > 0 
          ? conditions.length * caseHeight + (conditions.length - 1) * conditionGap 
          : caseHeight; // 如果没有条件，也给一个基本高度
        
        const caseVerticalOffset = 15; // 增加灰色方框的下移距离
        
        node.attr({
          [`case-box-${index}`]: {
            refX: 0.08,  // 相当于左侧留出 20px 的边距
            refY: (yPosition + caseVerticalOffset) / nodeHeight,  // 转换为相对节点高度的比例，向下移动
            width: caseBoxWidth,
            height: caseBoxHeight,
            rx: 4,
            ry: 4,
            fill: '#f5f5f5',
            stroke: '#e0e0e0',
            strokeWidth: 1
          }
        });
        
        // 设置分支标签(If/ElseIf) - 放在右上角位置
        node.attr({
          [`case-label-${index}`]: {
            text: index === 0 ? 'If' : 'ElseIf',
            refX: nodeWidth - 30, // 更加向左移动
            refY: yPosition + 5, // 标签位置保持不变
            fontSize: 14,
            fontWeight: 500,
            fill: '#333333',
            textAnchor: 'end', // 右对齐
            textVerticalAnchor: 'middle',
          }
        });
        
        // 移除视觉指示器，只使用真正的端口
        
        // 处理所有条件
        let currentY = yPosition + caseVerticalOffset; // 条件也要向下移动
        for (let condIndex = 0; condIndex < conditions.length; condIndex++) {
          const condition = conditions[condIndex];
          
          // 为每个条件添加元素
          // 添加条件框
          newMarkup.push({
            tagName: 'rect',
            selector: `condition-box-${index}-${condIndex}`
          });
          
          // 添加等号
          newMarkup.push({
            tagName: 'text',
            selector: `condition-eq-${index}-${condIndex}`
          });
          
          // 添加条件值
          newMarkup.push({
            tagName: 'text',
            selector: `condition-value-${index}-${condIndex}`
          });
          
          // 如果不是最后一个条件，添加运算符
          if (condIndex < conditions.length - 1) {
            newMarkup.push({
              tagName: 'text',
              selector: `condition-op-${index}-${condIndex}`
            });
          }
          
          // 设置条件框 - 现在不再需要单独的条件框，因为已经有了整个Case的框
        node.attr({
            [`condition-box-${index}-${condIndex}`]: {
              refX: 0.12, // 稍微缩进
              refY: currentY / nodeHeight, // 转换为比例
              width: caseBoxWidth - 50, // 缩小宽度，给右侧If/ElseIf留出空间
              height: caseHeight,
              rx: 4,
              ry: 4,
              fill: 'transparent', // 透明填充
              stroke: 'transparent', // 透明边框
              strokeWidth: 0
            }
          });
          
          // 获取条件的值和组件
          const conditionValue = this.formatCondition(condition);
          
          // 设置等号
          const comparatorSymbol = condition.comparator === 'equals' ? '=' : 
                               condition.comparator === 'notEquals' ? '≠' :
                               condition.comparator === 'greaterThan' ? '>' :
                               condition.comparator === 'lessThan' ? '<' :
                               condition.comparator === 'contains' ? '包含' : 
                               condition.comparator === 'notContains' ? '不包含' : '=';
        
        node.attr({
            [`condition-eq-${index}-${condIndex}`]: {
              text: comparatorSymbol,
              refX: 0.68, // 固定在右侧位置约70%处
              refY: (currentY + caseHeight / 2) / nodeHeight, // 转换为比例
              fontSize: 14,
              fontWeight: 400,
              fill: '#666666',
              textAnchor: 'middle',
              textVerticalAnchor: 'middle'
            }
          });
          
          // 设置条件值
          node.attr({
            [`condition-value-${index}-${condIndex}`]: {
              text: conditionValue,
              refX: 0.6, // 位于等号左侧约60%处
              refY: (currentY + caseHeight / 2) / nodeHeight, // 转换为比例
              fontSize: 14,
              fontWeight: 400,
              fill: '#666666',
              textAnchor: 'end',
              textVerticalAnchor: 'middle'
            }
          });
          
          currentY += caseHeight;
          
          // 如果不是最后一个条件，添加运算符
          if (condIndex < conditions.length - 1) {
            const operator = conditions[condIndex + 1].operator || 'and';
            
            node.attr({
              [`condition-op-${index}-${condIndex}`]: {
                text: operator,
                refX: 0.68, // 与等号对齐
                refY: (currentY + conditionGap / 2) / nodeHeight, // 转换为比例
                fontSize: 14,
                fontWeight: 400,
                fill: '#666666',
                textAnchor: 'middle',
                textVerticalAnchor: 'middle'
              }
            });
            
            currentY += conditionGap;
          }
        }
        
        // === 创建Case端口 ===
        const casePortId = `case-port-${index}`;
        this.createCasePort(node, casePortId, yPosition, index);
        
        // 空Case时也添加连接桩 (第二个case没有条件时也需要有连接桩)
        if (conditions.length === 0) {
          newMarkup.push({
            tagName: 'text',
            selector: `empty-case-hint-${index}`
          });
          
          // 添加提示文本
          node.attr({
            [`empty-case-hint-${index}`]: {
              text: '未设置条件',
              refX: 0.6, // 靠右放置，与条件值对齐
              refY: (firstCondY + caseVerticalOffset) / nodeHeight, // 转换为比例，考虑下移
              fontSize: 12,
              fontWeight: 400,
              fill: '#999999',
              textAnchor: 'end',
              textVerticalAnchor: 'middle'
            }
          });
          
          // === 创建空条件Case端口 ===
          const emptyCasePortId = `case-port-${index}`;
          this.createCasePort(node, emptyCasePortId, yPosition, index);
        }
      });
      
      // 使用固定位置策略定位Else分支
      // 不再依赖于最后一个分支的位置，而是使用固定位置
      // const firstBranchY = branchPositions.length > 0 ? branchPositions[0] : caseStartY;
      
      // // 固定Else分支的位置
      // let elseFixedY;
      
      // 计算节点的总高度 - 考虑所有分支的条件和间距
      let calculatedNodeHeight = nodeHeight;
      
      // 如果没有分支，使用一个固定的节点高度
      if (branchPositions.length === 0) {
        calculatedNodeHeight = titleHeight + 100; // 标题高度 + 固定空间
      } else {
        // 计算最后一个分支的底部位置
        const lastBranchPosition = branchPositions[branchPositions.length - 1];
        const lastCaseItem = realCases[realCases.length - 1];
        const conditions = lastCaseItem.conditions || [];
        
        // 计算最后一个分支的高度
        const caseVerticalOffset = 15;
        const conditionsHeight = conditions.length > 0 
          ? conditions.length * caseHeight + (conditions.length - 1) * conditionGap
          : caseHeight;
          
        // 计算节点的最小高度 - 最后一个分支底部 + 一些额外空间
        const lastBranchBottom = lastBranchPosition + conditionsHeight + caseVerticalOffset + 20;
        calculatedNodeHeight = Math.max(nodeHeight, lastBranchBottom + 60);
      }
      
      // 调整节点高度
      node.resize(nodeWidth, calculatedNodeHeight);
      
      // 计算Else分支的相对位置
      // 根据分支数量和条件数量动态调整refY值
      let elseRefY;
      
      if (branchPositions.length === 0) {
        // 没有分支时，放在中间偏下位置
        elseRefY = 0.6;
      } else if (branchPositions.length === 1) {
        // 只有一个分支时，根据条件数量调整
        const conditions = realCases[0].conditions || [];
        if (conditions.length <= 1) {
          elseRefY = 0.65; // 条件少，放在中间偏下
        } else if (conditions.length <= 3) {
          elseRefY = 0.75; // 条件适中，放在下方
        } else {
          elseRefY = 0.85; // 条件多，放在更下方
        }
      } else {
        // 多个分支时，根据最后一个分支的位置动态计算
        const lastBranchPosition = branchPositions[branchPositions.length - 1];
        const lastCaseItem = realCases[realCases.length - 1];
        const conditions = lastCaseItem.conditions || [];
        
        // 计算最后一个分支的底部位置
        const caseVerticalOffset = 15;
        const conditionsHeight = conditions.length > 0 
          ? conditions.length * caseHeight + (conditions.length - 1) * conditionGap
          : caseHeight;
        
        const lastBranchBottom = lastBranchPosition + conditionsHeight + caseVerticalOffset;
        
        // 计算最后一个分支底部在节点中的相对位置，加上一些间距
        const relativePosition = (lastBranchBottom + 30) / calculatedNodeHeight;
        
        // 确保refY值在合理范围内
        elseRefY = Math.min(Math.max(relativePosition, 0.6), 0.9);
      }
      
      console.log(`计算的Else相对位置: refY=${elseRefY}`);

      // 计算else端口的绝对Y位置
      const elseYPosition = elseRefY * calculatedNodeHeight;
      console.log(`[updateConditionNode] Else端口位置计算详情:`, {
        branchPositionsLength: branchPositions.length,
        realCasesLength: realCases.length,
        elseRefY: elseRefY,
        calculatedNodeHeight: calculatedNodeHeight,
        elseYPosition: elseYPosition,
        nodeWidth: nodeWidth,
        titleHeight: titleHeight
      });

      // 获取当前节点数据
      const currentData = node.getData() || {};

      // 更新节点数据，保存Else分支位置
      node.setData({
        ...currentData,
        elseRefY: elseRefY
      });

      // 不再添加盒子，只添加文本
      node.attr({
        'elseLabelWithCases': {
          text: `Else`,
          refX: nodeWidth - 30, // 使用refX替代x，固定在距离右边缘30px处
          refY: elseRefY, // 使用动态计算的refY值
          fontSize: 14,
          fontWeight: 500,
          fill: '#666666', // 使用灰色
          textAnchor: 'end', // 右对齐
          textVerticalAnchor: 'middle',
          dominantBaseline: 'middle',
        },
        'else-port-indicator': {
          cx: nodeWidth, // 放在节点右侧边缘，与case端口一致
          cy: elseYPosition, // 使用绝对Y位置，与case端口一致
          r: 6,
          fill: '#ffffff',
          stroke: '#6366F1',
          strokeWidth: 1,
          opacity: 1,
          visibility: 'visible',
          magnet: true // 使其可连接
        }
      });

      // 完全删除现有的ELSE端口
      const elsePorts = node.getPorts() || [];
      const outPorts = elsePorts.filter(port =>
        port.id === `${node.id}-port-out` ||
        port.id.startsWith('else-port-')
      );
      outPorts.forEach(port => {
        try {
          node.removePort(port.id);
        } catch (e) {
          console.error(`移除端口失败: ${port.id}`, e);
        }
      });
      
      // === 创建Else端口 ===
      const elsePortId = this.createElsePort(node, elseYPosition);

      // 重新连接之前保存的边
      setTimeout(() => {
        edgeConnections.forEach(connection => {
          if (connection.type === 'outgoing') {
            let newPortId = null;

            // 根据原端口类型重新连接
            if (connection.portId.startsWith('case-port-')) {
              // case端口：根据索引重新连接
              const portIndex = parseInt(connection.portId.replace('case-port-', ''));
              if (portIndex < realCases.length) {
                newPortId = `case-port-${portIndex}`;
              }
            } else if (connection.portId.startsWith('else-port-')) {
              // else端口：连接到新的else端口
              newPortId = elsePortId;
            }

            // 检查新端口是否存在并重新连接
            if (newPortId) {
              const newPort = node.getPort(newPortId);
              if (newPort) {
                console.log('[updateConditionNode] 重新连接输出边:', {
                  from: `${node.id}:${newPortId}`,
                  to: `${connection.targetCell}:${connection.targetPort}`
                });

                const edge = this.createEdgeWithTools();
                edge.setSource({ cell: node.id, port: newPortId });
                edge.setTarget({ cell: connection.targetCell, port: connection.targetPort });
                this.graph.addEdge(edge);
              }
            }
          } else if (connection.type === 'incoming') {
            // 重新连接输入端口的边
            const newPortId = `${node.id}-port-in`;

            const newPort = node.getPort(newPortId);
            if (newPort) {
              console.log('[updateConditionNode] 重新连接输入边:', {
                from: `${connection.sourceCell}:${connection.sourcePort}`,
                to: `${node.id}:${newPortId}`
              });

              const edge = this.createEdgeWithTools();
              edge.setSource({ cell: connection.sourceCell, port: connection.sourcePort });
              edge.setTarget({ cell: node.id, port: newPortId });
              this.graph.addEdge(edge);
            }
          }
        });
      }, 100);
    },
    
    // 格式化条件表达式
    formatConditions(conditions) {
      if (!conditions || conditions.length === 0) return '';
      
      // 如果只有一个条件，直接返回格式化后的条件
      if (conditions.length === 1) {
        return this.formatCondition(conditions[0]);
      }
      
      // 对于多个条件，我们只展示第一个条件
      const firstCondition = this.formatCondition(conditions[0]);
      
      // 只显示第一个条件
      return firstCondition;
    },
    
    // 格式化单个条件
    formatCondition(condition) {
      if (!condition) return '';
      
      // 这些标签可以在需要时使用
      // 为了保持简单，我们只显示值
      const value = condition.value || '';
      
      // 限制值的长度，避免太长
      const maxValueLength = 15;
      const displayValue = value.length > maxValueLength ? value.substring(0, maxValueLength) + '...' : value;
      
      return `${displayValue}`;
    },
    
    // 更新节点数据，供父组件调用
    updateNodeData(nodeId, data) {
      if (!this.graph) return;

      const node = this.graph.getCellById(nodeId);
      if (!node) return;

      // 获取当前节点数据
      const currentData = node.getData() || {};

      // 为Keywords节点添加特殊调试日志
      if (nodeId.includes('KeywordExtract') || currentData.type === 'keywords' || currentData.type === 'keywordNode') {
        console.log('[X6Graph] Keywords节点数据更新:');
        console.log('[X6Graph] nodeId:', nodeId);
        console.log('[X6Graph] 当前节点数据:', currentData);
        console.log('[X6Graph] 更新数据:', data);
        console.log('[X6Graph] 更新数据中的form.query:', data.data?.form?.query || data.form?.query);
      }

      // 为Categorize节点添加特殊调试日志
      if (nodeId.includes('Categorize') || currentData.type === 'classification' || currentData.type === 'categorizeNode') {
        console.log('[X6Graph] Categorize节点数据更新:');
        console.log('[X6Graph] nodeId:', nodeId);
        console.log('[X6Graph] 当前节点数据:', currentData);
        console.log('[X6Graph] 更新数据:', data);
        console.log('[X6Graph] 更新数据中的form.query:', data.data?.form?.query || data.form?.query);
      }
      
      // 处理条件节点的特殊情况
      if (currentData.type === 'conditions' && data.cases !== undefined) {
        // 确保完全脱离 Vue 响应式系统
        const casesDeepCopy = JSON.parse(JSON.stringify(data.cases || []));
        
        // 对于条件节点，完全替换数据而不是合并
        const newData = {
          type: 'conditions',
          modelType: currentData.modelType,
          modelId: currentData.modelId,
          modelName: data.title || currentData.modelName,
          id: currentData.id,
          title: data.title || currentData.title,
          elseAction: data.elseAction !== undefined ? data.elseAction : currentData.elseAction,
          cases: casesDeepCopy
        };
        
        // 强制重置节点数据
        node.data = null;
        
        // 设置新数据
        node.setData(newData);
        
        // 更新标题 - 确保使用中文名称
        if (data.title) {
          const chineseName = this.getChineseNameFromModelName(data.title, newData.type);
          node.attr('label/text', chineseName);
        }
        
        // 更新条件节点显示和连接桩
        this.updateConditionNode(node, casesDeepCopy, newData.elseAction);
        
        return;
      }
      
      // 合并现有数据和新数据
      const newData = { ...currentData, ...data };

      // 特殊处理Begin节点：当form.prologue更新时，同时更新output.content
      if ((currentData.type === 'beginNode' || nodeId === 'begin') && data.form && data.form.prologue) {
        console.log('[X6Graph] Begin节点开场白更新:', data.form.prologue);

        // 确保output结构存在
        if (!newData.output) {
          newData.output = {};
        }
        if (!newData.output.content) {
          newData.output.content = {};
        }

        // 更新output.content
        newData.output.content = {
          "0": {
            content: data.form.prologue
          }
        };

        console.log('[X6Graph] Begin节点output已更新:', newData.output);
      }

      // 更新节点数据
      node.setData(newData);
      
      // 如果有模型名称，更新节点标签 - 确保使用中文名称
      if (data.modelName) {
        // 如果modelName是英文标签，转换为中文名称
        const chineseName = this.getChineseNameFromModelName(data.modelName, newData.type || currentData.type);
        node.attr('label/text', chineseName);
      }
      
      // 如果是特定类型的节点，更新底部模型名称
      if (currentData.modelType === 'optimization' || 
          currentData.modelType === 'keywords' || 
          currentData.modelType === 'generation') {
        if (data.selectedModel) {
          node.attr('modelName/text', `使用: ${data.selectedModel}`);
        }
      }
      
            // 更新问题分类节点的模型名称和分类
      if (currentData.type === 'classification') {
        if (data.selectedModel) {
          node.attr({
            'modelName/text': `使用: ${data.selectedModel}`,
            'modelName/visibility': 'visible',
            'modelBox/visibility': 'visible'
          });
        }
        
        // 确保分类节点的颜色与ModelSelector中一致
        node.attr({
          'icon-bg': {
            fill: '#FFF7E8', // 问题分类节点的背景色
          },
          'icon': {
            fill: '#F59A23', // 问题分类节点的图标色
          }
        });
        
        // 如果更新了分类，重新设置连接桩
        if (data.categories) {
          this.updateClassificationNode(node, data.categories);
        } else {
          this.updateClassificationNode(node, []);
        }
      }
      
      // 更新条件节点的标题和Else分支显示
      if (currentData.type === 'conditions') {
        // 更新标题
        if (data.title) {
          node.attr('label/text', data.title);
        }
        
        // 使用updateConditionNode方法更新显示和连接桩
        const cases = currentData.cases || [];
        const elseAction = data.elseAction !== undefined ? data.elseAction : currentData.elseAction;
        this.updateConditionNode(node, cases, elseAction);
      }
    },
    
    // 根据操作类型获取显示标签
    getActionLabel(action) {
      const actionLabels = {
        'retrieval': '知识检索',
        'generation': '生成回答',
        'dialogue': '对话',
        'message': '静态消息',
        'optimization': '问题优化',
        'keywords': '关键词',
        'conditions': '条件',
        'hub': '集线器',
        'template': '模板转换',
        'loop': '循环',
        'code': '代码'
      };
      
      return actionLabels[action] || action;
    },
    
    // 清空工作流
    clearWorkflow() {
      if (!this.graph) return;
      
      // 清除所有节点和边
      this.graph.clearCells();
      
      // 重置节点ID计数器
      this.nodeIdCounter = 1;
      
      // 重新添加开始节点
      createStartNode(this.graph);
      
      console.log('工作流已清空');
    },

    // 从DSL加载工作流
    loadWorkflowFromDSL(dsl) {
      if (!this.graph || !dsl || !dsl.graph) {
        return;
      }
      // DSL加载逻辑
      try {
        this.clearWorkflow();
        // 过滤掉 iterationStartNode 节点
        const { nodes = [], edges = [] } = dsl.graph;
        const filteredNodes = nodes.filter(n => n.type !== 'iterationStartNode');
        
        const startNodeData = filteredNodes.find(n => n.type === 'beginNode' || n.type === 'start' || n.type === 'begin' || n.id === 'begin');



        let startPosition = startNodeData
          ? (startNodeData.position || startNodeData.positionAbsolute || { x: 100, y: 100 })
          : { x: 100, y: 100 };

        // 确保位置数据是普通对象，而不是Vue的响应式对象
        startPosition = {
          x: Number(startPosition.x) || 100,
          y: Number(startPosition.y) || 100
        };



        // 从DSL的components中获取Begin节点的实际数据
        let beginNodeData = null;
        if (dsl.components && dsl.components.begin) {
          beginNodeData = dsl.components.begin.obj;

          // 确保数据是纯对象，不是响应式对象
          beginNodeData = JSON.parse(JSON.stringify(beginNodeData));
        }

        createStartNode(this.graph, startPosition, beginNodeData);
        
        // 创建节点映射表
        const nodeMap = new Map();
        
        // 首先创建所有节点（跳过开始节点）
        filteredNodes.forEach(nodeData => {
          if (nodeData.type === 'start' || nodeData.type === 'beginNode' || nodeData.type === 'begin' || nodeData.id === 'begin') {
            // 跳过开始节点，因为已经存在
            return;
          }
          
          // 自动补全 categorizeNode 的 categories 和 selectedModel 字段
          if (
            (nodeData.type === 'categorizeNode' || nodeData.type === 'classification') &&
            nodeData.data && nodeData.data.form && nodeData.data.form.category_description
          ) {
            nodeData.data.categories = Object.keys(nodeData.data.form.category_description).map(key => ({
              name: key,
              ...nodeData.data.form.category_description[key]
            }));
            nodeData.data.selectedModel = nodeData.data.form.llm_id || '';
          }
          
          // 创建节点
          const node = this.createNodeFromDSL(nodeData);
          if (node) {
            const originalNodeId = nodeData.id || nodeData.nodeId;
            const actualNodeId = node.id;

            // 节点ID映射：原始ID -> 实际ID

            // 使用原始ID和实际ID都建立映射
            nodeMap.set(originalNodeId, node);
            nodeMap.set(actualNodeId, node);
          }
        });
        
        // 先处理分类节点和条件节点，再创建边
        setTimeout(() => {
          this.graph.getNodes().forEach(node => {
            const nodeData = node.getData() || {};

            // 处理分类节点
            if (node._pendingCategories) {
              this.updateClassificationNode(node, node._pendingCategories);
              delete node._pendingCategories; // 清理临时数据
            }

            // 处理条件节点 - 支持多种类型标识
            if (nodeData.type === 'conditions' || nodeData.modelType === 'conditions' ||
                nodeData.type === 'switchNode' || node.getData()?.type === 'conditions') {

              // 尝试从不同位置获取cases数据
              let cases = nodeData.cases || [];

              // 如果没有cases，尝试从form.conditions转换
              if ((!cases || cases.length === 0) && nodeData.form?.conditions) {
                cases = nodeData.form.conditions.map((condition, index) => ({
                  name: `Case ${index + 1}`,
                  conditions: condition.items || [condition], // 支持items数组或单个条件
                  to: condition.to
                }));
              }

              // 如果还是没有cases，创建一个默认的case
              if (!cases || cases.length === 0) {
                cases = [
                  {
                    name: 'Case 1',
                    conditions: [
                      {
                        cpn_id: '售前',
                        operator: '=',
                        value: ''
                      }
                    ]
                  }
                ];
              }


              this.updateConditionNode(node, cases, nodeData.elseAction);
            }
          });

          // 在所有节点处理完成后，再创建边
          setTimeout(() => {
            edges.forEach(edgeData => {
              this.createEdgeFromDSL(edgeData, nodeMap);
            });
          }, 100);
        }, 200);

        // 工作流加载完成
      } catch (error) {
        console.error('从DSL加载工作流失败:', error);
        throw error;
      }
    },
    
    // 从DSL数据创建节点
    createNodeFromDSL(nodeData) {
      if (!this.graph) return null;
      try {
        // 提取节点信息，DSL中的结构可能不同
        const id = nodeData.id || nodeData.nodeId || `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const position = nodeData.position || { x: 100, y: 100 };
        const data = nodeData.data || nodeData || {};
        if ((!data.form || Object.keys(data.form).length === 0) && nodeData.obj && nodeData.obj.params) {
          data.form = nodeData.obj.params;
        }
        // 根据节点类型创建对应的模型数据
        const modelData = this.convertNodeDataToModel(nodeData);
        // 创建节点
        const node = createNode(
          this.graph, 
          modelData, 
          position.x, 
          position.y, 
          id
        );
        // 设置节点数据
        if (node) {
          // 合并时优先保留 data.form，防止被 modelData.form 覆盖
          const nodeDataObj = { ...modelData, ...data, form: data.form };
          // 强制写入标准化 type
          nodeDataObj.type = modelData.type;
          node.setData(nodeDataObj);
          // 特殊处理注释节点，设置内容显示
          if (modelData.type === 'comment') {
            const content = modelData.content || data.content || data.text || data.description || '';
            const title = modelData.name || data.title || data.name || '注释';
            node.attr({
              'label/text': title,
              'content/text': content
            });
          }
          // 特殊处理问题分类节点，设置分类信息
          // 注意：不在这里调用 updateClassificationNode，而是在所有边创建完成后调用
          if (modelData.type === 'classification' && data.categories) {
            // 暂存分类数据，稍后处理
            node._pendingCategories = data.categories;
          }
        }
        return node;
      } catch (error) {
        return null;
      }
    },
    
    // 将节点数据转换为模型数据
    convertNodeDataToModel(nodeData) {
      const { type, data = {} } = nodeData;
      let model;
      // 标准化条件节点和模板节点类型
      let normalizedType = type;
      if (type === 'switchNode') normalizedType = 'conditions';
      if (type === 'templateNode') normalizedType = 'template';
      if (type === 'messageNode') normalizedType = 'message';
      // 标准化集线器节点类型
      if (type === 'logicNode' && (data.name === 'Concentrator' || data.label === 'Concentrator' || data.modelType === 'hub' || data.name === '集线器' || data.label === '集线器')) {
        normalizedType = 'hub';
      }
      // 精准兼容：logicNode类型根据label/modelType判断
      if (normalizedType === 'logicNode') {
        if (data.name === 'Switch' || data.label === 'Switch' || data.modelType === 'conditions') {
          model = {
            type: 'conditions',
            modelType: 'conditions',
            modelId: data.modelId || 'default',
            name: data.data?.name || data.title || '条件',
            form: data.data?.form || nodeData.obj?.params || {},
          };
        } else if (data.label === 'Answer' || data.modelType === 'dialogue' || data.type === 'dialogue') {
          model = {
            type: 'dialogue',
            modelType: 'dialogue',
            modelId: data.modelId || 'default',
            name: data.data?.name || data.title || '对话',
            form: data.data?.form || nodeData.obj?.params || {},
          };
        } else if (data.label === 'Generate' || data.modelType === 'generation' || data.type === 'generation') {
          model = {
            type: 'generation',
            modelType: 'generation',
            modelId: data.modelId || 'default',
            name: data.data?.name || data.title || '生成回答',
            form: data.data?.form || nodeData.obj?.params || {},
          };
        } else {
          model = {
            type: 'logicNode',
            modelType: 'logicNode',
            modelId: data.modelId || 'default',
            name: data.data?.name || data.title || '逻辑',
            form: data.data?.form || nodeData.obj?.params || {},
          };
        }
      } else {
        // 根据DSL中的节点类型返回对应的模型数据
        switch (normalizedType) {
          case 'beginNode':
            model = {
              type: 'start',
              modelType: 'start',
              modelId: data.modelId || 'default',
              name: data.title || '开始',
              form: nodeData.obj?.params || {},
            };
            break;
          case 'logicNode':
            console.log('[DSL加载调试] logicNode数据:', data);
            console.log('[DSL加载调试] data.data:', data.data);
            console.log('[DSL加载调试] data.data.name:', data.data?.name);
            console.log('[DSL加载调试] data.data.label:', data.data?.label);

            model = {
              type: 'dialogue',
              modelType: 'dialogue',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || data.name || '对话',
              form: data.data?.form || nodeData.obj?.params || {},
            };

            console.log('[DSL加载调试] 创建的model:', model);
            break;
          case 'ragNode':
            model = {
              type: 'code',
              modelType: 'code',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '代码',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'rewriteNode':
            model = {
              type: 'optimization',
              modelType: 'optimization',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '问题优化',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'keywordNode':
            model = {
              type: 'keywords',
              modelType: 'keywords',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '关键词提取',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'retrievalNode':
            model = {
              type: 'retrieval',
              modelType: 'retrieval',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '知识检索',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'noteNode':
            model = {
              type: 'comment',
              modelType: 'comment',
              modelId: data.modelId || 'default',
              name: data.name || data.title || data.label || '注释',
              content: (data.form && data.form.text) || '',
              bgColor: '#FFFBE6',
              iconColor: '#FAAD14',
              form: nodeData.obj?.params || {},
            };
            break;
          case 'generateNode':
            model = {
              type: 'generation',
              modelType: 'generation',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '生成回答',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'group':
            model = {
              type: 'loop',
              modelType: 'loop',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '循环',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'iterationStartNode':
            model = {
              type: 'iteration-item',
              modelType: 'iteration-item',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '迭代项',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'categorizeNode':
            model = {
              type: 'classification',
              modelType: 'classification',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '问题分类',
              categories: data.data?.categories || data.categories || [],
              selectedModel: data.selectedModel || '默认模型',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'conditions':
            model = {
              type: 'conditions',
              modelType: 'conditions',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '条件',
              cases: data.data?.cases || data.cases || [],
              elseAction: data.data?.elseAction || data.elseAction,
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          // 保留原有的类型映射作为备选
          case 'retrieval':
            model = {
              type: 'retrieval',
              modelType: 'retrieval',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '知识检索',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'generation':
            model = {
              type: 'generation',
              modelType: 'generation',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '生成回答',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'dialogue':
            model = {
              type: 'dialogue',
              modelType: 'dialogue',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '对话',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'message':
            model = {
              type: 'message',
              modelType: 'message',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '静态消息',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'optimization':
            model = {
              type: 'optimization',
              modelType: 'optimization',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '问题优化',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'keywords':
            model = {
              type: 'keywords',
              modelType: 'keywords',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '关键词',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'hub':
            model = {
              type: 'hub',
              modelType: 'hub',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '集线器',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'template':
            model = {
              type: 'template',
              modelType: 'template',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '模板转换',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'loop':
            model = {
              type: 'loop',
              modelType: 'loop',
              modelId: data.modelId || 'default',
              name: data.data?.name || data.title || '循环',
              form: data.data?.form || nodeData.obj?.params || {},
            };
            break;
          case 'code':
            model = {
              type: 'code',
              modelType: 'code',
              modelId: data.modelId || 'default',
              name: data.title || '代码',
              form: nodeData.obj?.params || {},
            };
            break;
          case 'comment':
            model = {
              type: 'comment',
              modelType: 'comment',
              modelId: data.modelId || 'default',
              name: data.title || '注释',
              form: nodeData.obj?.params || {},
            };
            break;
          case 'iteration-item':
            model = {
              type: 'iteration-item',
              modelType: 'iteration-item',
              modelId: data.modelId || 'default',
              name: data.title || 'IterationItem',
              form: nodeData.obj?.params || {},
            };
            break;
          default:
            console.warn('未知的节点类型:', normalizedType, '使用默认映射');
            model = {
              type: normalizedType,
              modelType: normalizedType,
              modelId: data.modelId || 'default',
              name: data.title || normalizedType,
              form: nodeData.obj?.params || {},
            };
        }
      }
      // 分类节点特殊处理 categories
      if ((normalizedType === 'categorizeNode' || normalizedType === 'classification') && nodeData.obj?.params?.category_description) {
        model.categories = parseCategoriesFromDSL(nodeData.obj.params.category_description);
        // 自动生成 handle → 端口id 映射表
        model.portHandleMap = buildPortHandleMap(model.categories, 'category-port-', 'name');
      }
      return model;
    },
    
    // 创建标准样式的边
    createEdgeWithTools() {
      return this.graph.createEdge({
        attrs: {
          line: {
            stroke: 'rgb(202, 197, 245)',
            strokeWidth: 2,
            targetMarker: {
              name: 'classic',
              size: 8,
              fill: 'rgb(202, 197, 245)',
              stroke: 'rgb(202, 197, 245)'
            }
          }
        },
        connector: {
          name: 'smooth',
          args: { radius: 30 }
        },
        router: {
          name: 'normal'
        },
        zIndex: 1001,
        // 添加一直显示的删除按钮工具
        tools: [
          {
            name: 'button-remove',
            args: {
              distance: 0.5, // 按钮位置在连线中点
              size: 16, // 按钮大小
              markup: [
                {
                  tagName: 'circle',
                  selector: 'button',
                  attrs: {
                    r: 8,
                    fill: '#8c8c8c', // 灰色背景
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    cursor: 'pointer',
                    opacity: 0.9
                  }
                },
                {
                  tagName: 'path',
                  selector: 'icon',
                  attrs: {
                    d: 'M -4 -4 4 4 M -4 4 4 -4', // X形状的删除图标
                    fill: 'none',
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    pointerEvents: 'none'
                  }
                }
              ]
            }
          }
        ]
      });
    },

    // 通用 handle → 端口 id 推断函数
    getPortIdByHandle(node, handle, direction = 'out') {
      // 端口查找逻辑

      if (!node) return undefined;

      // 如果没有handle，尝试找到唯一的端口
      if (!handle) {
        const groupPorts = node.getPorts().filter(p => p.group === direction);
        if (groupPorts.length === 1) {
          return groupPorts[0].id;
        }
        return undefined;
      }

      const data = node.getData() || {};
      // 1. 优先查 portHandleMap（如有）
      if (data.portHandleMap) {
        const portId = data.portHandleMap[String(handle).trim()];
        if (portId) return portId;
      }
      // 2. 分类节点处理
      if (data.type === 'classification' || Array.isArray(data.categories)) {
        // 输入端口：handle为 'a' 或其他标准输入标识
        if (direction === 'in' && (handle === 'a' || handle === 'in' || !handle)) {
          return `${node.id}-port-in`;
        }

        // 输出端口：分类名 → category-port-x
        if (direction === 'out' && Array.isArray(data.categories)) {
          const idx = data.categories.findIndex(cat => String(cat.name).trim() === String(handle).trim());
          if (idx !== -1) {
            return `category-port-${idx}`;
          }
        }
      }
      // 3. 条件节点处理
      if (data.type === 'conditions' || Array.isArray(data.cases)) {
        console.log('[getPortIdByHandle] 条件节点处理:', {
          nodeId: node.id,
          handle,
          direction,
          nodeType: data.type,
          cases: data.cases,
          ports: node.getPorts()
        });

        // 输入端口：handle为 'a' 或其他标准输入标识
        if (direction === 'in' && (handle === 'a' || handle === 'in' || !handle)) {
          const portId = `${node.id}-port-in`;
          console.log('[getPortIdByHandle] 条件节点输入端口:', portId);
          return portId;
        }

        // 输出端口处理
        if (direction === 'out') {
          // 处理 else 分支的各种表示方式
          if (handle === 'else' || handle === 'end_cpn_id' || handle === 'Else') {
            const elsePort = (node.getPorts() || []).find(p => p.id.startsWith('else-port-'));
            console.log('[getPortIdByHandle] 条件节点else端口:', { handle, elsePort: elsePort?.id });
            if (elsePort) return elsePort.id;
          }

          // 获取所有case端口，直接从节点端口列表中查找
          const casePorts = (node.getPorts() || []).filter(p => p.id.startsWith('case-port-'));
          console.log('[getPortIdByHandle] 找到的case端口:', casePorts.map(p => ({ id: p.id, group: p.group })));

          // 处理 case 分支 - 优先使用实际端口列表
          if (casePorts.length > 0) {
            // 支持 "Case 1", "Case 2" 格式
            const caseMatch = handle.match(/^Case\s+(\d+)$/i);
            if (caseMatch) {
              const idx = parseInt(caseMatch[1]) - 1; // Case 1 -> index 0
              if (idx >= 0 && idx < casePorts.length) {
                const portId = `case-port-${idx}`;
                console.log('[getPortIdByHandle] 条件节点case端口(Case N):', { handle, idx, portId });
                return portId;
              }
            }

            // 支持 a/b/c... 字母顺序
            if (/^[a-z]$/.test(handle)) {
              const idx = handle.charCodeAt(0) - 'a'.charCodeAt(0);
              if (idx < casePorts.length) {
                const portId = `case-port-${idx}`;
                console.log('[getPortIdByHandle] 条件节点case端口(字母):', { handle, idx, portId });
                return portId;
              }
            }
          }

          // 备用：如果有data.cases，也尝试匹配
          if (Array.isArray(data.cases) && data.cases.length > 0) {
            // 先查 name/label/value
            let idx = data.cases.findIndex(c => c.name === handle || c.label === handle || c.value === handle);
            if (idx !== -1) {
              const portId = `case-port-${idx}`;
              console.log('[getPortIdByHandle] 条件节点case端口(name/label/value):', { handle, idx, portId });
              return portId;
            }
          }
        }

        console.log('[getPortIdByHandle] 条件节点端口查找失败:', { handle, direction });
      }
      // 4. 循环节点、其他类型可扩展
      // ...可按需扩展...
      // 5. 默认：如果只有一个端口，直接返回
      const groupPorts = node.getPorts().filter(p => p.group === direction);
      if (groupPorts.length === 1) {
        return groupPorts[0].id;
      }

      // 6. 条件节点特殊处理：如果查找输出端口但没找到，尝试查找out端口组
      if (direction === 'out' && data.type === 'conditions') {
        const outPorts = node.getPorts().filter(p => p.group === 'out');
        console.log('[getPortIdByHandle] 条件节点out端口组查找:', {
          handle,
          outPorts: outPorts.map(p => ({ id: p.id, group: p.group }))
        });

        // 如果只有一个out端口，直接返回
        if (outPorts.length === 1) {
          return outPorts[0].id;
        }
      }

      return undefined;
    },

    // 根据 DSL 边数据创建连线，保证 source/target 唯一性
    createEdgeFromDSL(edgeData, nodeMap) {
      try {
        let { source, target, sourcePort, targetPort, sourceHandle, targetHandle } = edgeData;

        // 处理边数据

      // 1. 查找节点
      if (source === 'begin' || source === 'beginNode' || source === 'start' || source === 'startNode') {
        source = 'begin'; // 修复：使用正确的开始节点ID
      }

      const sourceNode = nodeMap.get(source) || this.graph.getCellById(source);
      const targetNode = nodeMap.get(target) || this.graph.getCellById(target);
      if (!sourceNode || !targetNode) {
        console.warn('无法创建边：找不到源节点或目标节点', {
          source,
          target,
          sourceNode: sourceNode?.id,
          targetNode: targetNode?.id,
          edgeData
        });
        return;
      }

      // 2. 通过 handle 查找 portId
      if (!sourcePort) {
        sourcePort = this.getPortIdByHandle(sourceNode, sourceHandle, 'out');
      }
      if (!targetPort) {
        targetPort = this.getPortIdByHandle(targetNode, targetHandle, 'in');
      }

      // 调试端口查找结果
      if (!sourcePort || !targetPort) {
        console.warn('[createEdgeFromDSL] 端口查找失败:', {
          edgeData,
          sourcePort,
          targetPort,
          sourceHandle,
          targetHandle,
          sourceNodeId: sourceNode.id,
          targetNodeId: targetNode.id,
          sourceNodeType: sourceNode.getData()?.type,
          targetNodeType: targetNode.getData()?.type,
          sourcePorts: sourceNode.getPorts(),
          targetPorts: targetNode.getPorts()
        });
        return null; // 端口查找失败时返回null
      }

      // 3. 创建边，保证 source/target 唯一
      const edge = this.createEdgeWithTools();

      // 如果端口存在，使用端口连接；否则使用节点连接
      if (sourcePort) {
        edge.setSource({ cell: sourceNode.id, port: sourcePort });
      } else {
        edge.setSource({ cell: sourceNode.id });
      }

      if (targetPort) {
        edge.setTarget({ cell: targetNode.id, port: targetPort });
      } else {
        edge.setTarget({ cell: targetNode.id });
      }

      // 样式已在创建时设置，无需重复设置

      this.graph.addEdge(edge);

      return edge;
      } catch (error) {
        console.error('[createEdgeFromDSL] 创建边失败:', error);
        console.error('[createEdgeFromDSL] 失败的边数据:', edgeData);
        return null;
      }
    },
    
    // 将内部节点类型映射到目标JSON格式的节点类型
    getTargetNodeType(internalType) {
      const typeMapping = {
        'dialogue': 'logicNode',
        'retrieval': 'retrievalNode',
        'generation': 'generateNode',
        'classification': 'categorizeNode',
        'message': 'messageNode',
        'optimization': 'rewriteNode',
        'keywords': 'keywordNode',
        'conditions': 'switchNode',
        'template': 'templateNode',
        'hub': 'logicNode',
        'loop': 'group',
        'code': 'ragNode',
        'comment': 'noteNode',
        'beginNode': 'beginNode',
        'iterationStartNode': 'iterationStartNode'
      };

      return typeMapping[internalType] || internalType;
    },



    // 获取当前图表的完整数据
    getGraphData() {
      if (!this.graph) {
        return null;
      }
      try {
        // 导出所有节点，但排除循环内容区域节点
        const nodes = this.graph.getNodes().filter(node => {
          const nodeData = node.getData() || {};
          // 排除循环内容区域节点（UI容器，不是DSL组件）
          if (nodeData.isLoopContent || nodeData.type === 'loopContent') {
            console.log(`[图表数据] 过滤掉循环内容区域节点: ${node.id}`);
            return false;
          }
          // 包含所有其他节点，包括IterationItem
          console.log(`[图表数据] 包含节点: ${node.id}, 类型: ${nodeData.type || 'undefined'}`);
          return true;
        });
        const edges = this.graph.getEdges();
        const graphData = {
          nodes: nodes.map(node => {
            const nodeData = node.getData() || {};
            const position = node.getPosition();
            const size = node.getSize();

            let data;
            if ((nodeData.type || node.type) === 'beginNode' || node.id === 'begin') {
              // Begin节点也需要保留完整的数据，特别是form和output
              data = {
                label: nodeData.label || 'Begin',
                name: nodeData.name || '开始',
                form: nodeData.form || {},
                output: nodeData.output || {},
                type: nodeData.type || 'beginNode'
              };

            } else {
              // 获取节点的中文显示名称和英文标签
              const chineseName = this.getChineseNameForNode(nodeData);
              const englishLabel = this.getEnglishLabelForNode(nodeData);

              data = {
                form: nodeData.form || {},
                label: englishLabel,
                name: chineseName
              };

              // 确保特定节点类型的form字段有必需的数组
              if (node.type === 'switchNode') {
                // 强制确保Switch节点有正确的form结构
                data.form = {
                  conditions: [],
                  ...data.form
                };
                console.log('[Switch节点] 修复后的form:', data.form);
              }
              if (node.type === 'messageNode') {
                // 强制确保Message节点有正确的form结构
                data.form = {
                  messages: [],
                  ...data.form
                };
              }

              // IterationItem节点特殊处理：确保有正确的类型和父节点信息
              if (nodeData.type === 'iterationStartNode' || nodeData.isIterationItem) {
                data = {
                  form: nodeData.form || {},
                  label: 'IterationItem', // 固定为IterationItem
                  name: 'IterationItem'   // 固定为IterationItem
                };
                console.log(`[图表数据] IterationItem节点 ${node.id} 数据:`, data);
              }

              // 分类节点特殊处理：自动同步 category_description
              if (
                (nodeData.type === 'categorizeNode' || nodeData.type === 'classification') &&
                Array.isArray(nodeData.settings?.categories)
              ) {
                const category_description = {};
                nodeData.settings.categories.forEach((cat, idx) => {
                  category_description[cat.name] = {
                    index: idx,  // 🔥 方案3：始终使用数组索引，确保递增索引
                    to: cat.to || cat.nextStep || ''
                  };
                });
                data.form = {
                  ...data.form,
                  category_description
                };
              }

              // 关键词节点特殊处理：确保 form 数据完整
              if (nodeData.type === 'keywordNode' || nodeData.type === 'keywords') {
                data.name = nodeData.name || '关键词_0';
                // 确保 form 数据结构完整
                if (!data.form || Object.keys(data.form).length === 0) {
                  data.form = {
                    frequencyPenaltyEnabled: false,
                    frequency_penalty: 0.7,
                    llm_id: nodeData.selectedModel || "qwen-max@Tongyi-Qianwen",
                    maxTokensEnabled: false,
                    max_tokens: 256,
                    presencePenaltyEnabled: false,
                    presence_penalty: 0.4,
                    query: [],
                    temperature: 0.1,
                    temperatureEnabled: false,
                    topPEnabled: false,
                    top_n: 3,
                    top_p: 0.3
                  };
                }
              }

              // 问题优化节点特殊处理：确保 form 数据完整
              if (nodeData.type === 'rewriteNode' || nodeData.type === 'optimization') {
                data.name = nodeData.name || '问题优化_0';
                // 确保 form 数据结构完整
                if (!data.form || Object.keys(data.form).length === 0) {
                  data.form = {
                    frequencyPenaltyEnabled: false,
                    frequency_penalty: 0.7,
                    language: "",
                    llm_id: nodeData.selectedModel || "qwen-max@Tongyi-Qianwen",
                    maxTokensEnabled: false,
                    max_tokens: 256,
                    message_history_window_size: 6,
                    presencePenaltyEnabled: false,
                    presence_penalty: 0.4,
                    temperature: 0.1,
                    temperatureEnabled: false,
                    topPEnabled: false,
                    top_p: 0.3
                  };
                }
              }

              // 问题分类节点特殊处理：确保 form 数据完整
              if (nodeData.type === 'categorizeNode' || nodeData.type === 'classification') {
                data.name = nodeData.name || '问题分类_0';
                // 确保 form 数据结构完整
                if (!data.form || Object.keys(data.form).length === 0) {
                  data.form = {
                    category_description: {},
                    frequencyPenaltyEnabled: false,
                    frequency_penalty: 0.7,
                    llm_id: nodeData.selectedModel || "qwen-max@Tongyi-Qianwen",
                    maxTokensEnabled: false,
                    max_tokens: 256,
                    message_history_window_size: 1,
                    presencePenaltyEnabled: false,
                    presence_penalty: 0.4,
                    query: [],
                    temperature: 0.1,
                    temperatureEnabled: false,
                    topPEnabled: false,
                    top_p: 0.3
                  };
                }

                // 从 categories 构建 category_description
                if (Array.isArray(nodeData.settings?.categories)) {
                  const category_description = {};
                  nodeData.settings.categories.forEach((cat, idx) => {
                    category_description[cat.name] = {
                      index: idx,  // 🔥 方案3：始终使用数组索引，确保递增索引
                      to: cat.to || cat.nextStep || ''
                    };
                  });
                  data.form.category_description = category_description;
                }
              }
            }
            // 调试信息
            const originalType = nodeData.type || node.type;
            const targetType = this.getTargetNodeType(originalType);

            const nodeResult = {
              id: node.id,
              type: targetType,
              data,
              position: { x: position.x, y: position.y },
              measured: { width: size.width, height: size.height },
              dragging: nodeData.dragging || false,
              selected: nodeData.selected || false,
              sourcePosition: nodeData.sourcePosition || 'right',
              targetPosition: nodeData.targetPosition || 'left'
            };

            // Iteration节点需要添加width和height字段
            if (targetType === 'group' && nodeData.isLoop) {
              nodeResult.width = size.width;
              nodeResult.height = size.height;
              console.log(`[图表数据] Iteration ${node.id} 添加width/height: ${size.width}x${size.height}`);
            }

            // IterationItem节点需要添加parentId字段
            if (nodeData.type === 'iterationStartNode' || nodeData.isIterationItem) {
              if (nodeData.parentLoopId) {
                nodeResult.parentId = nodeData.parentLoopId;
                nodeResult.extent = 'parent'; // 添加extent字段
                console.log(`[图表数据] IterationItem ${node.id} 添加parentId: ${nodeData.parentLoopId}`);
              }
            }

            return nodeResult;
          }),
          edges: edges.map(edge => {
            const source = edge.getSource();
            const target = edge.getTarget();

            // 生成符合第一个DSL格式的ID
            const sourceId = source.cell;
            const targetId = target.cell;
            const targetHandle = this.convertToSimpleHandle(target.port);
            let sourceHandle = this.convertToSimpleHandle(source.port);

            // 🔥 方案2：在保存时强制转换分类组件的sourceHandle
            sourceHandle = this.convertCategoryHandleToName(sourceId, source.port, sourceHandle);

            // 构建描述性ID，格式：xy-edge__source-target+handle
            let edgeId = `xy-edge__${sourceId}-${targetId}`;
            if (sourceHandle && sourceHandle !== 'c' && sourceHandle !== 'd') {
              // 🔥 如果sourceHandle是分类名称，将其添加到ID中
              edgeId = `xy-edge__${sourceId}${sourceHandle}-${targetId}`;
            }
            if (targetHandle) {
              edgeId += targetHandle;
            }

            // 构建边的基本结构
            const edgeResult = {
              id: edgeId,
              markerEnd: "logo",
              source: sourceId,
              target: targetId,
              type: "buttonEdge"
            };

            // 添加端口处理 - 使用分类名称或简单字母格式
            if (sourceHandle && sourceId !== 'begin') {
              edgeResult.sourceHandle = sourceHandle;
            }
            if (targetHandle) {
              edgeResult.targetHandle = targetHandle;
            }

            // 设置样式 - 使用RGB格式
            edgeResult.style = {
              stroke: "rgb(202 197 245)",
              strokeWidth: 2
            };

            // 设置较高的zIndex
            edgeResult.zIndex = 1001;

            return edgeResult;
          })
        };
        return graphData;
      } catch (error) {
        return null;
      }
    },

    // 导出DSL格式的工作流数据
    exportDSL() {
      const graphData = this.getGraphData();
      if (!graphData) {
        return {};
      }

      // 将图表数据转换为DSL格式
      return {
        graph: graphData
      };
    },

    // 获取节点的中文显示名称
    getChineseNameForNode(nodeData) {
      // 如果节点有自定义的中文名称，优先使用
      if (nodeData.title) {
        return nodeData.title;
      }

      // 如果节点有modelName，使用它
      if (nodeData.modelName) {
        return nodeData.modelName;
      }

      // 根据节点类型返回默认中文名称
      const nodeType = nodeData.type || nodeData.modelType;

      const chineseNameMapping = {
        'dialogue': '对话',
        'retrieval': '知识检索',
        'generation': '生成回答',
        'classification': '问题分类',
        'message': '静态消息',
        'optimization': '问题优化',
        'keywords': '关键词',
        'conditions': '条件',
        'template': '模板转换',
        'hub': '集线器',
        'loop': '循环',
        'code': '代码',
        'comment': '注释'
      };

      const mappedName = chineseNameMapping[nodeType];
      const fallbackName = nodeData.name;
      const finalName = mappedName || fallbackName || '未命名';

      return finalName;
    },

    // 获取节点的英文标签（用于系统内部识别）
    getEnglishLabelForNode(nodeData) {
      // 根据节点类型返回英文标签
      const nodeType = nodeData.type || nodeData.modelType;

      const englishLabelMapping = {
        'dialogue': 'Answer',
        'retrieval': 'Retrieval',
        'generation': 'Generate',
        'classification': 'Categorize',
        'message': 'Message',
        'optimization': 'RewriteQuestion',
        'keywords': 'KeywordExtract',
        'conditions': 'Switch',
        'template': 'Template',
        'hub': 'Concentrator',
        'loop': 'Iteration',
        'code': 'Code',
        'comment': 'Note'
      };

      const mappedLabel = englishLabelMapping[nodeType];
      const fallbackLabel = nodeData.label;
      const finalLabel = mappedLabel || fallbackLabel || 'Unknown';

      return finalLabel;
    },

    // 从modelName获取中文名称（处理英文标签转中文的情况）
    getChineseNameFromModelName(modelName, nodeType) {
      // 如果modelName已经是中文，直接返回
      if (modelName && !/^[A-Za-z]+$/.test(modelName)) {
        return modelName;
      }

      // 如果是英文标签，转换为中文
      const englishToChinese = {
        'Answer': '对话',
        'Retrieval': '知识检索',
        'Generate': '生成回答',
        'Categorize': '问题分类',
        'Message': '静态消息',
        'RewriteQuestion': '问题优化',
        'KeywordExtract': '关键词',
        'Switch': '条件',
        'Template': '模板转换',
        'Concentrator': '集线器',
        'Iteration': '循环',
        'Code': '代码',
        'Note': '注释'
      };

      // 如果能找到对应的中文名称，返回中文
      if (englishToChinese[modelName]) {
        return englishToChinese[modelName];
      }

      // 否则根据节点类型返回默认中文名称
      const chineseNameMapping = {
        'dialogue': '对话',
        'retrieval': '知识检索',
        'generation': '生成回答',
        'classification': '问题分类',
        'message': '静态消息',
        'optimization': '问题优化',
        'keywords': '关键词',
        'conditions': '条件',
        'template': '模板转换',
        'hub': '集线器',
        'loop': '循环',
        'code': '代码',
        'comment': '注释'
      };

      return chineseNameMapping[nodeType] || modelName || '未命名';
    },

    // 🔥 方案2：将分类组件的字母handle转换为分类名称
    convertCategoryHandleToName(sourceId, originalPort, currentHandle) {
      // 如果不是字母格式的handle，直接返回
      if (!currentHandle || !/^[c-z]$/.test(currentHandle)) {
        return currentHandle;
      }

      // 查找源节点
      const sourceNode = this.graph.getCellById(sourceId);
      if (!sourceNode) {
        return currentHandle;
      }

      // 检查是否是分类节点
      const nodeData = sourceNode.getData() || {};
      const isClassificationNode = nodeData.type === 'classification' ||
                                  nodeData.modelType === 'classification' ||
                                  (nodeData.categories && Array.isArray(nodeData.categories));

      if (!isClassificationNode) {
        return currentHandle;
      }

      // 获取分类数据
      let categories = null;
      if (nodeData.categories && Array.isArray(nodeData.categories)) {
        categories = nodeData.categories;
      } else if (nodeData.settings && nodeData.settings.categories) {
        categories = nodeData.settings.categories;
      } else if (nodeData.form && nodeData.form.categories) {
        categories = nodeData.form.categories;
      }

      if (!categories || !Array.isArray(categories)) {
        return currentHandle;
      }

      // 将字母转换为索引 (c=0, d=1, e=2, ...)
      const index = currentHandle.charCodeAt(0) - 99; // 'c'.charCodeAt(0) = 99

      if (index >= 0 && index < categories.length && categories[index] && categories[index].name) {
        const categoryName = categories[index].name;
        return categoryName;
      }

      return currentHandle;
    },

    // 将复杂的端口名称转换为简单字母格式
    convertToSimpleHandle(portName) {
      if (!portName) return null;

      // 如果已经是简单字母，直接返回
      if (/^[a-z]$/.test(portName)) {
        return portName;
      }

      // 如果是中文分类名称（包含中文字符），直接返回原名称
      if (/[\u4e00-\u9fa5]/.test(portName)) {
        return portName;
      }

      // 端口名称映射规则
      const portMapping = {
        // 输入端口
        'in': 'c',
        'port-in': 'c',
        'input': 'c',
        // 输出端口
        'out': 'b',
        'port-out': 'b',
        'output': 'b',
        // 开始节点端口
        'start-port-out': null, // 开始节点不需要sourceHandle
        // 分类端口
        'category-port-0': 'c',
        'category-port-1': 'd',
        'category-port-2': 'e',
        'category-port-3': 'f',
        // 条件端口 - 使用Case N格式
        'case-port-0': 'Case 1',
        'case-port-1': 'Case 2',
        'case-port-2': 'Case 3',
        'case-port-3': 'Case 4'
      };

      // 检查直接映射
      if (Object.prototype.hasOwnProperty.call(portMapping, portName)) {
        return portMapping[portName];
      }

      // 处理带节点ID的端口名称，如 "Answer_0-port-in"
      if (portName.includes('-port-in')) {
        // 🔥 根据组件类型确定正确的输入端口
        // 从端口名称中提取节点ID
        const nodeId = portName.split('-port-in')[0];

        // 查找对应的节点来确定组件类型
        const node = this.graph.getCellById(nodeId);
        if (node) {
          const nodeData = node.getData();
          const componentName = nodeData?.name || nodeData?.label || nodeData?.type;

          // Categorize组件使用端口 'a'
          if (componentName && (componentName.includes('Categorize') || componentName.includes('分类'))) {
            return 'a';
          }
        }

        // 其他组件默认使用端口 'c'
        return 'c';
      }
      if (portName.includes('-port-out')) {
        return 'b';
      }

      // 处理分类端口，如 "category-port-1"
      const categoryMatch = portName.match(/category-port-(\d+)/);
      if (categoryMatch) {
        const index = parseInt(categoryMatch[1]);

        // 🔥 改进的分类名称获取逻辑

        // 方法1: 从端口的attrs中直接获取分类名称（最直接的方法）
        const nodes = this.graph.getNodes();
        for (const node of nodes) {
          const ports = node.getPorts();
          const matchingPort = ports.find(p => p.id === portName);
          if (matchingPort) {
            // 🔥 优先从端口数据中获取分类名称
            if (matchingPort.categoryName) {
              return matchingPort.categoryName;
            }

            // 方法2: 从节点数据中获取分类名称
            const nodeData = node.getData();

            // 尝试多种数据结构
            let categories = null;

            // 尝试1: nodeData.categories
            if (nodeData.categories && Array.isArray(nodeData.categories)) {
              categories = nodeData.categories;
            }
            // 尝试2: nodeData.settings.categories
            else if (nodeData.settings && nodeData.settings.categories && Array.isArray(nodeData.settings.categories)) {
              categories = nodeData.settings.categories;
            }
            // 尝试3: nodeData.form.categories
            else if (nodeData.form && nodeData.form.categories && Array.isArray(nodeData.form.categories)) {
              categories = nodeData.form.categories;
            }

            if (categories && categories[index] && categories[index].name) {
              return categories[index].name;
            }

            break;
          }
        }

        // 方法2: 如果方法1失败，尝试从边的上下文中获取
        const edges = this.graph.getEdges();
        for (const edge of edges) {
          const source = edge.getSource();
          if (source && source.port === portName) {
            const sourceNode = edge.getSourceNode();
            if (sourceNode) {
              const sourceData = sourceNode.getData();

              // 尝试获取分类数据
              let categories = null;
              if (sourceData.categories && Array.isArray(sourceData.categories)) {
                categories = sourceData.categories;
              } else if (sourceData.settings && sourceData.settings.categories) {
                categories = sourceData.settings.categories;
              }

              if (categories && categories[index] && categories[index].name) {
                return categories[index].name;
              }
            }
          }
        }

        // 方法3: 最后尝试从端口名称模式推断分类名称
        const commonCategories = ['售前', '售后', '技术支持', '投诉建议', '其他'];
        if (index < commonCategories.length) {
          return commonCategories[index];
        }
        // 如果所有方法都失败，回退到字母格式
        return String.fromCharCode(99 + index); // c, d, e, f...
      }

      // 处理条件端口，如 "case-port-1"
      const caseMatch = portName.match(/case-port-(\d+)/);
      if (caseMatch) {
        const index = parseInt(caseMatch[1]);
        return `Case ${index + 1}`; // Case 1, Case 2, Case 3...
      }

      // 处理else端口，如 "else-port-1753429558740"
      if (portName.match(/else-port-/)) {
        return 'end_cpn_id'; // 保持与JSON数据一致
      }

      // 默认返回 'c'
      return 'c';
    }
  }
}
</script>

<style>
.x6-graph-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 智能体标题栏样式 */
.agent-title-bar {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  background: rgba(255,255,255,0.9);
  padding: 6px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  pointer-events: none;
}

.graph-container {
  width: 100%;
  height: 100%;
}

.node-hover-menu {
  position: absolute;
  z-index: 1000;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: visible;
  border: 1px solid #eaeaea;
  min-width: 100px;
  animation: fadeIn 0.15s ease-in-out;
}

/* 为循环内部节点的菜单添加特殊样式 */
.node-hover-menu.inside-loop {
  background-color: #f0f8ff; /* 浅蓝色背景 */
  border: 1px solid #1890FF; /* 蓝色边框 */
  box-shadow: 0 2px 12px rgba(24, 144, 255, 0.2); /* 蓝色阴影 */
}

/* 为循环内部节点的菜单添加箭头指向 */
.node-hover-menu.inside-loop::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #f0f8ff;
}

/* 为循环内部节点的菜单添加边框箭头 */
.node-hover-menu.inside-loop::before {
  content: '';
  position: absolute;
  bottom: -9px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-top: 9px solid #1890FF;
  z-index: -1;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 当鼠标悬停在节点上时使菜单按钮更加明显 */
.x6-node:hover text[data-shape="menuButton"] {
  fill: #409EFF;
  font-size: 22px;
  font-weight: 600;
}

/* 增加菜单的可点击区域 */
.menu-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.menu-item i {
  margin-right: 10px;
  font-size: 16px;
  color: #606266;
}

.menu-item span {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.menu-item:hover span {
  color: #409EFF;
}

.menu-item:hover i {
  color: #409EFF;
}

/* 循环内容区域样式 */
.x6-node[data-shape="loop-content"] rect[data-shape="body"] {
  stroke-dasharray: 5 5;
  fill: rgba(245, 245, 245, 0.5); /* 半透明浅灰色背景，匹配图片 */
  stroke: #1890FF; /* 蓝色边框，匹配图标 */
  cursor: move; /* 移动光标 */
  pointer-events: auto; /* 允许接收鼠标事件，使内容区域可拖动 */
}

/* 循环内容区域的连接桩样式 */
.x6-node[data-shape="loop-content"] circle[data-shape="portBody"] {
  pointer-events: auto; /* 确保连接桩可以接收鼠标事件 */
}

/* 循环内容区域可拖动 */
.x6-node[data-shape="loop-content"] {
  pointer-events: auto; /* 允许接收鼠标事件，使内容区域可拖动 */
}

/* 调整大小的手柄样式 */
.x6-node[data-shape="loop-content"] rect[data-shape="resizeHandle"] {
  fill: #1890FF !important;
  stroke: #fff !important;
  stroke-width: 1.5 !important;
  cursor: se-resize !important;
  pointer-events: auto !important; /* 确保手柄可以接收鼠标事件 */
  opacity: 0.9 !important;
  transition: all 0.2s !important;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.4)) !important;
}

.x6-node[data-shape="loop-content"] path[data-shape="resizeIcon"] {
  stroke: #fff !important;
  stroke-width: 2.5 !important;
  pointer-events: none !important;
}

.x6-node[data-shape="loop-content"]:hover rect[data-shape="resizeHandle"] {
  opacity: 1 !important;
  fill: #40a9ff !important;
  transform: scale(1.2) !important;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.6) !important;
}

.x6-node[data-shape="loop-content"]:hover path[data-shape="resizeIcon"] {
  stroke-width: 3 !important;
}

.x6-node[data-shape="loop-content"]:hover text[data-shape="resizeText"] {
  opacity: 1 !important;
  transition: opacity 0.3s !important;
}

/* 当有节点被选中且在循环内时，高亮循环内容区域 */
.x6-node[data-shape="loop-content"].content-active rect[data-shape="body"] {
  fill: rgba(245, 245, 245, 0.7); /* 更明显的背景 */
  stroke: #1890FF; /* 蓝色边框，匹配图标 */
  stroke-width: 1.5px;
  stroke-dasharray: 5 5;
}

/* 当循环节点被选中时，内容区域也应该有相应的样式 */
.x6-node[data-shape="loop"].x6-node-selected + .x6-node[data-shape="loop-content"] rect[data-shape="body"] {
  stroke: #1890FF;
  stroke-width: 2px;
  stroke-dasharray: none; /* 使用实线而不是虚线 */
}

/* 添加一个辅助类，用于标记处于循环内的节点 */
.x6-node.in-loop-content {
  /* 可以添加一个细微的视觉提示，表明节点在循环内 */
  filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.4)); /* 蓝色阴影，匹配图标 */
}

/* IterationItem 节点样式 */
.x6-node[data-shape="iteration-item"] rect[data-shape="body"] {
  fill: #ffffff;
  stroke: #1890FF;
  stroke-width: 1px;
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2));
}

.x6-node[data-shape="iteration-item"] rect[data-shape="icon-bg"] {
  fill: #E8F7FF;
  stroke: transparent;
}

.x6-node[data-shape="iteration-item"] path[data-shape="icon"] {
  fill: #1890FF;
}

.x6-node[data-shape="iteration-item"] text[data-shape="label"] {
  font-size: 14px;
  font-weight: 500;
  fill: #333;
}

.x6-node[data-shape="iteration-item"] text[data-shape="description"] {
  font-size: 12px;
  fill: #666;
}

.x6-node[data-shape="iteration-item"]:hover {
  filter: drop-shadow(0 2px 8px rgba(24, 144, 255, 0.3));
}

.x6-node[data-shape="iteration-item"].x6-node-selected rect[data-shape="body"] {
  stroke: #1890FF;
  stroke-width: 2px;
}

/* IterationItem 连接桩样式 */
.x6-node[data-shape="iteration-item"] circle[data-shape="portBody"] {
  fill: #ffffff;
  stroke: #1890FF;
  stroke-width: 1px;
}

.x6-node[data-shape="iteration-item"] text[data-shape="portText"] {
  fill: #1890FF;
  font-size: 15px;
}

/* 添加不可拖动的视觉提示 */
.x6-node[data-shape="iteration-item"] {
  cursor: default;
  pointer-events: auto;
}

.x6-node[data-shape="iteration-item"]:hover::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px dashed #1890FF;
  border-radius: 10px;
  opacity: 0.5;
  pointer-events: none;
}

.x6-node[data-shape="iteration-item"]::before {
  content: '固定位置';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  color: #1890FF;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.x6-node[data-shape="iteration-item"]:hover::before {
  opacity: 1;
}

/* 注释节点特殊样式 */
.x6-node[data-shape="comment"] rect[data-shape="body"] {
  fill: #FFFBE6;
  stroke: #F5DD98;
  stroke-width: 1px;
}

.x6-node[data-shape="comment"] rect[data-shape="header"] {
  fill: #FFF8DC;
  stroke: #F5DD98;
}

.x6-node[data-shape="comment"] text[data-shape="label"] {
  font-size: 16px;
  font-weight: 500;
}

.x6-node[data-shape="comment"] text[data-shape="content"] {
  font-size: 14px;
  fill: #666;
  white-space: pre-wrap;
}

.x6-node[data-shape="comment"]:hover {
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
}

.x6-node[data-shape="comment"].x6-node-selected rect[data-shape="body"] {
  stroke: #FAAD14;
  stroke-width: 2px;
}

/* 循环节点特殊样式 */
.x6-node[data-shape="loop"] rect[data-shape="body"] {
  fill: #F5F5F5; /* 浅灰色背景，匹配图片 */
  stroke: #E0E0E0;
}

.x6-node[data-shape="loop"] rect[data-shape="icon-bg"] {
  fill: #E8F7FF; /* 浅蓝色背景，与ModelSelector中一致 */
  stroke: transparent; /* 移除边框 */
  stroke-width: 0;
}

.x6-node[data-shape="loop"] path[data-shape="icon"] {
  fill: #1890FF; /* 蓝色图标，与ModelSelector中一致 */
  transform: scale(0.8);
}

.x6-node[data-shape="loop"] text[data-shape="label"] {
  font-size: 16px;
  font-weight: 500;
  fill: #333;
}

.x6-node[data-shape="loop"]:hover {
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
}

.x6-node[data-shape="loop"].x6-node-selected rect[data-shape="body"] {
  stroke: #1890FF; /* 蓝色边框，匹配图标 */
  stroke-width: 2px;
}

/* 🔧 新增：内联编辑样式 */
.comment-content-wrapper {
  transition: all 0.2s ease;
}

.comment-content-editable {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 4px;
  margin: -4px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ddd transparent;
}

.comment-content-editable::-webkit-scrollbar {
  width: 6px;
}

.comment-content-editable::-webkit-scrollbar-track {
  background: transparent;
}

.comment-content-editable::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.comment-content-editable::-webkit-scrollbar-thumb:hover {
  background-color: #bbb;
}

.comment-content-editable:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.comment-content-editable[contenteditable="true"] {
  background-color: #f8f9fa !important;
  border: 2px solid #409EFF !important;
  cursor: text !important;
}

.comment-content-editable[contenteditable="false"] {
  cursor: pointer;
}

.comment-content-editable:empty::before {
  content: attr(data-placeholder);
  color: #999;
  font-style: italic;
}

.comment-hint {
  transition: opacity 0.2s ease;
}
</style>