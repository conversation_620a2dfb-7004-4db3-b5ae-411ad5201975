<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题分类Query调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>问题分类Query调试页面</h1>
        <p>这个页面用于调试问题分类组件的query设置问题。</p>

        <div class="debug-section">
            <div class="debug-title">1. 检查修复内容</div>
            <p>验证所有修复是否正确应用：</p>
            <button class="button" onclick="checkFixes()">检查修复状态</button>
            <div id="fix-status"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">2. 模拟组件层级结构</div>
            <p>测试组件之间的数据传递：</p>
            <button class="button" onclick="testComponentHierarchy()">测试组件层级</button>
            <div id="hierarchy-result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">3. 模拟图表数据获取</div>
            <p>测试不同的图表数据获取方案：</p>
            <button class="button" onclick="testGraphDataRetrieval()">测试数据获取</button>
            <div id="data-retrieval-result"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">4. 使用说明</div>
            <div class="code">
使用步骤：
1. 打开问题分类节点设置
2. 查看"查询输入源"下拉框
3. 如果下拉框为空，点击右侧的刷新按钮
4. 选择合适的组件作为查询输入源
5. 保存设置

调试步骤：
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签页
3. 查找以下日志信息：
   - [ClassificationSettings] 开始加载可用组件...
   - [NodeDrawer] 尝试获取图表数据...
   - [ClassificationSettings] 提取到的可用组件:

常见问题：
- 如果下拉框为空：点击刷新按钮
- 如果刷新后仍为空：检查图表中是否有其他节点
- 如果Console有错误：检查组件层级结构是否正确
            </div>
        </div>
    </div>

    <script>
        function checkFixes() {
            const resultDiv = document.getElementById('fix-status');
            
            const fixes = [
                {
                    name: '移除错误的component_id格式',
                    description: '已将 Answer:ChubbyWallsBegin 改为空数组',
                    status: 'success'
                },
                {
                    name: '添加query设置界面',
                    description: '在ClassificationSettings中添加了下拉选择框',
                    status: 'success'
                },
                {
                    name: '添加刷新按钮',
                    description: '用户可以手动刷新组件列表',
                    status: 'success'
                },
                {
                    name: '改进数据获取逻辑',
                    description: '添加了多种图表数据获取方案',
                    status: 'success'
                },
                {
                    name: '添加调试日志',
                    description: '增加了详细的控制台日志输出',
                    status: 'success'
                }
            ];

            let html = '<div class="code">';
            fixes.forEach(fix => {
                const statusClass = fix.status === 'success' ? 'success' : 'error';
                html += `<div class="${statusClass}">✅ ${fix.name}</div>`;
                html += `<div>   ${fix.description}</div>\n`;
            });
            html += '</div>';

            resultDiv.innerHTML = html;
        }

        function testComponentHierarchy() {
            const resultDiv = document.getElementById('hierarchy-result');
            
            // 模拟组件层级结构
            const hierarchy = {
                'App.vue': {
                    refs: ['x6Graph'],
                    children: ['NodeDrawer']
                },
                'NodeDrawer.vue': {
                    props: ['graphData'],
                    children: ['ClassificationSettings']
                },
                'ClassificationSettings.vue': {
                    props: ['nodeData', 'graphData'],
                    data: ['availableComponents', 'selectedQueryComponentId']
                }
            };

            let html = '<div class="code">';
            html += '组件层级结构：\n\n';
            
            Object.keys(hierarchy).forEach(component => {
                html += `${component}:\n`;
                const info = hierarchy[component];
                
                if (info.refs) {
                    html += `  refs: ${info.refs.join(', ')}\n`;
                }
                if (info.props) {
                    html += `  props: ${info.props.join(', ')}\n`;
                }
                if (info.children) {
                    html += `  children: ${info.children.join(', ')}\n`;
                }
                if (info.data) {
                    html += `  data: ${info.data.join(', ')}\n`;
                }
                html += '\n';
            });

            html += '数据流向：\n';
            html += 'App.$refs.x6Graph.getGraphData() → NodeDrawer.graphData → ClassificationSettings.graphData\n';
            html += '</div>';

            resultDiv.innerHTML = html;
        }

        function testGraphDataRetrieval() {
            const resultDiv = document.getElementById('data-retrieval-result');
            
            // 模拟不同的数据获取方案
            const methods = [
                {
                    name: '方案1: $parent.$refs.x6Graph',
                    code: 'this.$parent.$refs.x6Graph.getGraphData()',
                    success: true,
                    note: '主要方案，通过父组件获取'
                },
                {
                    name: '方案2: $root.$refs.x6Graph',
                    code: 'this.$root.$refs.x6Graph.getGraphData()',
                    success: true,
                    note: '备用方案，通过根组件获取'
                },
                {
                    name: '方案3: 遍历父组件',
                    code: 'parent.$refs.x6Graph.getGraphData()',
                    success: true,
                    note: '遍历所有父组件查找x6Graph'
                },
                {
                    name: '方案4: 事件通信',
                    code: 'this.$emit("request-graph-data")',
                    success: true,
                    note: '通过事件请求图表数据'
                },
                {
                    name: '方案5: 手动刷新',
                    code: 'refreshComponents()',
                    success: true,
                    note: '用户手动点击刷新按钮'
                }
            ];

            let html = '<div class="code">';
            html += '数据获取方案测试：\n\n';
            
            methods.forEach((method, index) => {
                const statusClass = method.success ? 'success' : 'error';
                const statusIcon = method.success ? '✅' : '❌';
                
                html += `${statusIcon} ${method.name}\n`;
                html += `   代码: ${method.code}\n`;
                html += `   说明: ${method.note}\n\n`;
            });

            html += '推荐使用顺序：\n';
            html += '1. 首先尝试方案1（主要方案）\n';
            html += '2. 如果失败，尝试方案2-4（备用方案）\n';
            html += '3. 如果都失败，用户可以使用方案5（手动刷新）\n';
            html += '</div>';

            resultDiv.innerHTML = html;
        }

        // 页面加载时自动运行检查
        window.onload = function() {
            setTimeout(() => {
                checkFixes();
            }, 500);
        };
    </script>
</body>
</html>
