<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流保存Query测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .step {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
        }
        .flow-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .flow-step {
            background: #f0f8ff;
            border: 2px solid #4a90e2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .flow-step.critical {
            background: #fff3e0;
            border-color: #ff9800;
        }
        .arrow {
            text-align: center;
            font-size: 24px;
            color: #4a90e2;
            margin: 10px 0;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            font-size: 16px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工作流保存Query完整测试</h1>
        <p>确保关键词组件的query能够正确保存到工作流DSL中</p>

        <div class="test-section">
            <div class="test-title">🎯 目标</div>
            <div class="success">
                <strong>最终目标：</strong>在保存的工作流DSL中，KeywordExtract_0的query格式正确：
                <div class="code">
"KeywordExtract_0": {
  "obj": {
    "params": {
      "query": [
        {
          "component_id": "Answer_0",
          "type": "reference"
        }
      ]
    }
  }
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 完整数据流程</div>
            <div class="flow-diagram">
                <div class="flow-step">
                    <strong>1. 节点设置</strong><br>
                    在KeywordsSettings中<br>
                    设置query
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>2. 节点保存</strong><br>
                    点击"保存设置"<br>
                    更新图表节点数据
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step critical">
                    <strong>3. 工作流保存</strong><br>
                    点击顶部"保存"按钮<br>
                    重新构建DSL
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>4. DSL构建</strong><br>
                    buildComponentsFromGraph<br>
                    读取节点数据
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>5. 最终保存</strong><br>
                    DSL保存到服务器<br>
                    包含正确的query
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 完整测试步骤</div>
            
            <div class="step">
                <strong>阶段1：设置关键词节点</strong>
                <ul class="checklist">
                    <li>双击关键词节点，打开设置</li>
                    <li>在"查询输入源"下拉框中选择"Answer_0"</li>
                    <li>查看控制台日志：</li>
                </ul>
                <div class="code">
[KeywordsSettings] selectedQueryComponentId变化: Answer_0
[KeywordsSettings] 更新query为: [{component_id: "Answer_0", type: "reference"}]
                </div>
                <ul class="checklist">
                    <li>点击"保存设置"按钮</li>
                    <li>查看完整的保存日志链</li>
                    <li>确认看到"保存节点设置"日志</li>
                </ul>
            </div>

            <div class="step">
                <strong>阶段2：验证节点数据更新</strong>
                <ul class="checklist">
                    <li>关闭设置抽屉</li>
                    <li>重新打开关键词节点设置</li>
                    <li>确认"查询输入源"仍然选中Answer_0</li>
                    <li>这证明节点数据已正确更新</li>
                </ul>
            </div>

            <div class="step">
                <strong>阶段3：保存整个工作流</strong>
                <ul class="checklist">
                    <li>点击顶部导航栏的"保存"按钮</li>
                    <li>查看控制台日志，寻找关键信息：</li>
                </ul>
                <div class="code">
开始保存工作流，Agent ID: xxx
获取到的图表数据: {...}
构建的组件数据: {...}
[DSL生成] 节点KeywordExtract_0: {...}
[DSL生成] formQuery: [{component_id: "Answer_0", type: "reference"}]
准备保存的DSL数据: {...}
                </div>
                <ul class="checklist">
                    <li>特别关注"准备保存的DSL数据"中的KeywordExtract_0</li>
                    <li>确认query不再是空数组</li>
                </ul>
            </div>

            <div class="step">
                <strong>阶段4：验证保存结果</strong>
                <ul class="checklist">
                    <li>等待保存完成提示</li>
                    <li>刷新页面重新加载工作流</li>
                    <li>再次查看关键词节点设置</li>
                    <li>确认query设置保持不变</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 关键检查点</div>
            
            <div class="warning">
                <strong>检查点1：节点保存是否成功</strong>
                <p>在阶段1完成后，必须看到以下日志：</p>
                <div class="code">
[NodeDrawer] 处理Keywords节点保存数据: {...}
[X6Graph] Keywords节点数据更新: {...}
保存节点设置: {...}
                </div>
                <p>如果没有看到，说明节点数据没有正确保存到图表中</p>
            </div>

            <div class="warning">
                <strong>检查点2：DSL构建是否读取正确数据</strong>
                <p>在阶段3中，必须看到：</p>
                <div class="code">
[DSL生成] 节点KeywordExtract_0: {...}
[DSL生成] formQuery: [{component_id: "Answer_0", type: "reference"}]
                </div>
                <p>如果formQuery仍然是空数组，说明图表节点数据没有正确更新</p>
            </div>

            <div class="warning">
                <strong>检查点3：最终DSL是否正确</strong>
                <p>在"准备保存的DSL数据"日志中，展开KeywordExtract_0，确认：</p>
                <div class="code">
KeywordExtract_0: {
  obj: {
    params: {
      query: [{component_id: "Answer_0", type: "reference"}]  // ✅ 不是空数组
    }
  }
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚨 常见问题排查</div>
            
            <div class="error">
                <strong>问题1：节点保存后，重新打开设置时query丢失</strong>
                <p><strong>原因：</strong>NodeDrawer或X6Graph的数据更新有问题</p>
                <p><strong>解决：</strong>检查保存日志，确认数据传递链完整</p>
            </div>

            <div class="error">
                <strong>问题2：DSL构建时读取到空query</strong>
                <p><strong>原因：</strong>图表节点的form.query没有正确更新</p>
                <p><strong>解决：</strong>检查X6Graph的updateNodeData方法</p>
            </div>

            <div class="error">
                <strong>问题3：保存后刷新页面，设置丢失</strong>
                <p><strong>原因：</strong>工作流保存到服务器时数据不完整</p>
                <p><strong>解决：</strong>检查handleSave方法和API调用</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 立即行动</div>
            <div class="step">
                <strong>现在请按照上述步骤完整操作：</strong>
                <ol>
                    <li><strong>设置关键词节点query</strong>（选择Answer_0并保存）</li>
                    <li><strong>验证节点数据更新</strong>（重新打开设置确认）</li>
                    <li><strong>保存整个工作流</strong>（点击顶部保存按钮）</li>
                    <li><strong>验证最终结果</strong>（检查DSL数据）</li>
                </ol>
                <p><strong>在每个阶段，请仔细查看控制台日志，如果有任何异常，请告诉我具体的日志内容。</strong></p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 成功标志</div>
            <div class="success">
                <strong>如果一切正常，你应该看到：</strong>
                <ol>
                    <li>节点设置保存时有完整的日志链</li>
                    <li>重新打开设置时query选择保持</li>
                    <li>工作流保存时DSL构建日志显示正确的formQuery</li>
                    <li>最终DSL数据中query格式正确</li>
                    <li>刷新页面后设置仍然保持</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
