/**
 * 循环节点相关功能模块
 */
import { Shape } from '@antv/x6';
import { getSvgPathForType } from './nodeUtils';

/**
 * 创建循环节点
 * @param {Object} graph - X6图表实例
 * @param {Object} model - 循环节点模型数据
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的循环节点
 */
export function createLoopNode(graph, model, id, x, y) {
  console.log('[循环创建] 开始创建循环节点，参数:', { model, id, x, y });
  console.log('[循环创建] 准备创建节点，检查Shape.Rect:', Shape.Rect);
  const node = new Shape.Rect({
    id,
    x,
    y,
    width: 500, // 设置整个group宽度为500
    height: 70, // 设置标题高度为70
    attrs: {
      body: {
        fill: '#ffffff', // 浅灰色背景，匹配图片
        stroke: '#E0E0E0', // 浅灰色边框
        strokeWidth: 1,
        rx: 10,
        ry: 10,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 3,
            blur: 10,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      label: {
        text: model.name || '循环',
        fill: '#333',
        fontSize: 18,
        fontWeight: '500',
        refX: 0.55,
        refY: 0.5,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
      },
      // 添加三个点的菜单按钮
      menuButton: {
        text: '⋮', // 三个垂直点
        fill: '#606266',
        fontSize: 20,
        fontWeight: 'bold',
        refX: '100%',
        refX2: -25,
        refY: 0.5,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        cursor: 'pointer',
        pointerEvents: 'auto', // 启用事件捕获
        opacity: 1,
      },
      // 添加菜单按钮的点击区域
      menuArea: {
        width: 40,
        height: 40,
        fill: 'transparent', // 透明填充
        stroke: 'transparent', // 确保没有边框
        strokeWidth: 0,
        strokeDasharray: 'none',
        refX: '100%',
        refX2: -35,
        refY: 0.5,
        refY2: -20,
        cursor: 'pointer',
        pointerEvents: 'auto',
        opacity: 0, // 完全透明
      },
      // 添加图标
      'icon-bg': {
        width: 36,
        height: 36,
        rx: 6,
        ry: 6, 
        fill: '#E8F7FF', // 浅蓝色背景，与ModelSelector中一致
        stroke: 'transparent',
        strokeWidth: 0,
        refX: 25,
        refY: '50%',
        refY2: -18,
      },
      'icon': {
        d: getSvgPathForType('loop'),
        fill: '#1890FF', // 蓝色图标，与ModelSelector中一致
        refX: 25,
        refY: '50%',
        refY2: -18,
        transform: 'scale(0.8) translate(10, 10)',
      },

    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
      {
        tagName: 'circle',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
      {
        tagName: 'rect',
        selector: 'menuArea',
      },
      {
        tagName: 'text',
        selector: 'menuButton',
      },

    ],
    data: {
      type: 'group', // 保持原有类型，用于UI识别
      modelType: model.type,
      modelId: model.id,
      modelName: model.name || '循环',
      label: 'Iteration',
      name: model.name || '循环_0',
      isLoop: true,
      childNodes: [], // 存储子节点的ID
      // 添加表单数据结构，用于DSL构建
      form: {
        delimiter: ',' // 默认分隔符
      }
    },
  });
  
  // 添加节点到画布
  console.log('[循环创建] 节点创建完成，markup:', node.getMarkup());
  console.log('[循环创建] 节点attrs:', node.getAttrs());
  graph.addNode(node);
  console.log('[循环创建] 节点已添加到画布');
  
  // 创建循环内容区域
  createLoopContent(graph, id, x, y + 65); // 减小与循环节点的间隔
  
  // 设置节点的自定义属性，用于CSS选择器
  const nodeView = graph.findViewByCell(node);
  if (nodeView && nodeView.container) {
    nodeView.container.setAttribute('data-shape', 'loop');
  }

  // 添加缩放功能
  console.log('[循环创建] 准备添加缩放功能到节点:', node.id);
  try {
    setupLoopResize(graph, node);
    console.log('[循环创建] 缩放功能添加完成');
  } catch (error) {
    console.error('[循环创建] 添加缩放功能时出错:', error);
  }

  return node;
}

/**
 * 创建循环内容区域
 * @param {Object} graph - X6图表实例
 * @param {String} loopId - 循环节点ID
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的内容区域节点
 */
export function createLoopContent(graph, loopId, x, y) {
  const contentId = `${loopId}-content`;
  const contentNode = new Shape.Rect({
    id: contentId,
    x,
    y: y - 5, // 减小与循环节点的间隔
    width: 500, // 与循环节点宽度一致
    height: 300, // 增加高度，使内容区域更大
    attrs: {
      body: {
        fill: 'rgba(245, 245, 245, 0.5)', // 半透明浅灰色背景，匹配图片
        stroke: '#1890FF', // 蓝色边框，匹配图标
        strokeWidth: 1,
        strokeDasharray: '5 5', // 虚线边框
        rx: 5,
        ry: 5,
        cursor: 'move', // 添加移动光标
      },
      // 添加提示文本
      hintText: {
        text: '可拖入节点到循环区域内',
        fill: '#999',
        fontSize: 12,
        fontStyle: 'italic',
        refX: 0.5,
        refY: 0.5,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        pointerEvents: 'none',
      },
      // 添加调整大小的手柄
      resizeHandle: {
        x: '100%',
        y: '100%',
        x2: -20,
        y2: -20,
        width: 20,
        height: 20,
        fill: '#1890FF',
        stroke: '#fff',
        strokeWidth: 1.5,
        rx: 2,
        ry: 2,
        cursor: 'se-resize',
        pointerEvents: 'auto',
        opacity: 0.8, // 增加默认可见度
      },
      // 添加调整大小的图标
      resizeIcon: {
        d: 'M 5,15 L 15,5 M 10,15 L 15,10',
        stroke: '#fff',
        strokeWidth: 2,
        x: '100%',
        y: '100%',
        x2: -20,
        y2: -20,
        pointerEvents: 'none',
      },
      // 添加"调整大小"文字提示
      resizeText: {
        text: '调整大小',
        fill: '#fff',
        fontSize: 10,
        fontWeight: 'bold',
        x: '100%',
        y: '100%',
        x2: -70,
        y2: -10,
        opacity: 0,
        pointerEvents: 'none',
      }
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'hintText',
      },
      {
        tagName: 'rect',
        selector: 'resizeHandle',
      },
      {
        tagName: 'path',
        selector: 'resizeIcon',
      },
      {
        tagName: 'text',
        selector: 'resizeText',
      }
    ],
    ports: {
      groups: {
        left: {
          position: {
            name: 'absolute',
            args: { x: 0, y: '45%' },
          },
          attrs: {
            portBody: {
              r: 10,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#1890FF', // 蓝色边框，匹配图标
            },
            portText: {
              text: '+',
              fill: '#1890FF', // 蓝色文本，匹配图标
              fontSize: 15,
              textAnchor: 'middle',
              textVerticalAnchor: 'middle',
              pointerEvents: 'none',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
            {
              tagName: 'text',
              selector: 'portText',
            },
          ],
        },
        right: {
          position: {
            name: 'absolute',
            args: { x: '100%', y: '45%' },
          },
          attrs: {
            portBody: {
              r: 10,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#1890FF', // 蓝色边框，匹配图标
            },
            portText: {
              text: '+',
              fill: '#1890FF', // 蓝色文本，匹配图标
              fontSize: 15,
              textAnchor: 'middle',
              textVerticalAnchor: 'middle',
              pointerEvents: 'none',
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
            {
              tagName: 'text',
              selector: 'portText',
            },
          ],
        },
      },
      items: [
        { id: `${contentId}-port-left`, group: 'left' },
        { id: `${contentId}-port-right`, group: 'right' },
      ],
    },
    zIndex: -1, // 确保内容区域在其他节点下方
    data: {
      type: 'loopContent', // 添加明确的类型标识
      isLoopContent: true,
      parentId: loopId,
      label: 'LoopContent',
      name: '循环内容区域'
    }
  });
  
  // 添加内容区域到画布
  graph.addNode(contentNode);
  
  // 设置节点的自定义属性
  const nodeView = graph.findViewByCell(contentNode);
  if (nodeView && nodeView.container) {
    nodeView.container.setAttribute('data-shape', 'loop-content');
  }
  
  // 创建IterationItem节点作为循环内的起点和输入节点
  console.log(`[循环创建] 为循环 ${loopId} 创建IterationItem`);
  createIterationItem(graph, loopId, contentId, x + 60, y + 60);
  
  return contentNode;
}

/**
 * 创建循环项节点（IterationItem）
 * @param {Object} graph - X6图表实例
 * @param {String} loopId - 循环节点ID
 * @param {String} contentId - 内容区域节点ID
 * @param {Number} x - X坐标
 * @param {Number} y - Y坐标
 * @returns {Object} 创建的循环项节点
 */
export function createIterationItem(graph, loopId, contentId, x, y) {
  const itemId = `${loopId}-item`;
  console.log(`[IterationItem创建] 创建IterationItem: ${itemId}, 父循环: ${loopId}`);

  const iterationItem = new Shape.Rect({
    id: itemId,
    x,
    y,
    width: 60,
    height: 60,
    // 禁止拖动，确保位置固定
    draggable: false,
    attrs: {
      body: {
        fill: '#ffffff',
        stroke: '#1890FF',
        strokeWidth: 1,
        rx: 8,
        ry: 8,
        filter: {
          name: 'dropShadow',
          args: {
            dx: 0,
            dy: 2,
            blur: 8,
            color: 'rgba(0,0,0,0.08)',
          },
        },
      },
      // 添加图标
      'icon-bg': {
        width: 28,
        height: 28,
        rx: 5,
        ry: 5, 
        fill: '#E8F7FF',
        stroke: 'transparent',
        strokeWidth: 0,
        refX: 20,
        refY: '50%',
        refY2: -14,
      },
      'icon': {
        d: 'M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z',
        fill: '#1890FF',
        refX: 20,
        refY: '50%',
        refY2: -14,
        transform: 'scale(0.6) translate(8, 8)',
      },
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'rect',
        selector: 'icon-bg',
      },
      {
        tagName: 'path',
        selector: 'icon',
      },
      {
        tagName: 'rect',
        selector: 'menuArea',
      },
      {
        tagName: 'text',
        selector: 'menuButton',
      }
    ],
    ports: {
      groups: {
        out: {
          position: {
            name: 'absolute',
            args: { x: '100%', y: '50%' },
          },
          attrs: {
            portBody: {
              r: 8,
              magnet: true,
              strokeWidth: 1,
              fill: '#ffffff',
              stroke: '#1890FF',
            },
            portText: {
              text: '+',
              fill: '#1890FF',
              fontSize: 15,
              textAnchor: 'middle',
              textVerticalAnchor: 'middle',
              refX: 0.5,
              refY: 0.5,
            },
          },
          markup: [
            {
              tagName: 'circle',
              selector: 'portBody',
            },
            {
              tagName: 'text',
              selector: 'portText',
            },
          ],
        },
      },
      items: [
        { id: `${itemId}-port-out`, group: 'out' },
      ],
    },
    data: {
      type: 'iterationStartNode',
      label: 'IterationItem',
      name: 'IterationItem',
      isIterationItem: true,
      parentLoopId: loopId,
      parentContentId: contentId,
      // 添加表单数据结构，用于DSL构建
      form: {},
      // 添加模型信息
      modelType: 'iteration-item',
      modelId: 'iteration-item'
    },
  });
  
  // 添加到画布
  graph.addNode(iterationItem);

  console.log(`[IterationItem创建] IterationItem ${itemId} 创建完成`);
  console.log(`[IterationItem创建] 节点数据:`, iterationItem.getData());
  console.log(`[IterationItem创建] 节点已添加到图表，当前图表节点数:`, graph.getNodes().length);
  
  // 设置节点的自定义属性
  const nodeView = graph.findViewByCell(iterationItem);
  if (nodeView && nodeView.container) {
    nodeView.container.setAttribute('data-shape', 'iteration-item');
  }
  
  // 将IterationItem添加到循环节点的子节点列表中
  const loopNode = graph.getCellById(loopId);
  if (loopNode) {
    const loopData = loopNode.getData() || {};
    if (!loopData.childNodes) {
      loopData.childNodes = [];
    }
    loopData.childNodes.push(itemId);
    loopNode.setData(loopData);
  }
  
  return iterationItem;
}

/**
 * 设置循环节点的缩放功能
 * @param {Object} graph - X6图表实例
 * @param {Object} node - 循环节点
 */
export function setupLoopResize(graph, node) {
  console.log('[缩放调试] 开始设置缩放功能，节点ID:', node.id);

  const nodeView = graph.findViewByCell(node);
  if (!nodeView) {
    console.error('[缩放调试] 找不到节点视图');
    return;
  }
  console.log('[缩放调试] 找到节点视图');

  let isResizing = false;
  let startSize = { width: 0, height: 0 };
  let startPosition = { x: 0, y: 0 };

  // 获取缩放手柄元素
  console.log('[缩放调试] 查找缩放手柄元素...');
  console.log('[缩放调试] 节点视图容器:', nodeView.container);

  // 尝试多种方式查找缩放手柄
  let resizeHandle = nodeView.container.querySelector('[data-selector="resizeHandle"]');
  if (!resizeHandle) {
    console.log('[缩放调试] 通过data-selector未找到，尝试其他方式...');
    resizeHandle = nodeView.container.querySelector('.resizeHandle');
  }
  if (!resizeHandle) {
    console.log('[缩放调试] 通过class未找到，尝试直接查找rect元素...');
    const allRects = nodeView.container.querySelectorAll('rect');
    console.log('[缩放调试] 找到的所有rect元素:', allRects);
    // 查找最后一个rect元素（可能是缩放手柄）
    if (allRects.length > 0) {
      resizeHandle = allRects[allRects.length - 1];
      console.log('[缩放调试] 使用最后一个rect作为缩放手柄:', resizeHandle);
    }
  }

  if (!resizeHandle) {
    console.error('[缩放调试] 找不到缩放手柄元素');
    console.log('[缩放调试] 容器内所有元素:', nodeView.container.innerHTML);
    console.log('[缩放调试] 查找所有selector元素:', nodeView.container.querySelectorAll('[data-selector]'));
    console.log('[缩放调试] 节点markup:', node.getMarkup());
    console.log('[缩放调试] 节点attrs:', node.getAttrs());
    console.log('[缩放调试] 容器中所有子元素:', nodeView.container.children);

    // 尝试手动创建缩放手柄
    console.log('[缩放调试] 尝试手动创建缩放手柄...');
    console.log('[缩放调试] 节点容器:', nodeView.container);
    console.log('[缩放调试] 容器的父元素:', nodeView.container.parentElement);
    console.log('[缩放调试] 容器是否在DOM中:', document.contains(nodeView.container));

    const svg = nodeView.container.querySelector('svg') || nodeView.container;
    console.log('[缩放调试] 选择的SVG容器:', svg);
    console.log('[缩放调试] SVG容器类型:', svg.tagName);

    if (svg) {
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('width', '16');
      rect.setAttribute('height', '16');
      rect.setAttribute('fill', '#ffffff');
      rect.setAttribute('stroke', '#1890FF');
      rect.setAttribute('stroke-width', '1.5');
      rect.setAttribute('rx', '3');
      rect.setAttribute('ry', '3');
      rect.setAttribute('opacity', '0.9');
      // 计算group的实际尺寸来确定缩放图标位置
      // group的高度应该包括标题(70) + 内容区域的高度
      const groupWidth = 500; // group宽度
      const groupHeight = 320; // group总高度，继续往下移动

      const handleX = groupWidth - 16;  // group宽度 - 手柄宽度
      const handleY = groupHeight - 16; // group高度 - 手柄高度

      rect.setAttribute('x', handleX.toString()); // 484 (500-16)
      rect.setAttribute('y', handleY.toString()); // 304 (320-16)

      console.log('[缩放调试] 缩放图标位置设置为group右下角:', {
        groupWidth,
        groupHeight,
        handleX,
        handleY
      });
      rect.setAttribute('cursor', 'nw-resize');
      rect.setAttribute('data-selector', 'resizeHandle');
      rect.setAttribute('pointer-events', 'all'); // 确保可以接收事件

      console.log('[缩放调试] 准备添加手柄到容器...');
      svg.appendChild(rect);
      // 创建对角箭头图标 - 简洁的双向对角箭头
      const arrowIcon = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      arrowIcon.setAttribute('transform', `translate(${handleX + 2}, ${handleY + 2})`);
      arrowIcon.setAttribute('pointer-events', 'none');

      // 左上到右下的箭头
      const arrow1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      arrow1.setAttribute('d', 'M2,2 L10,10 M7,7 L10,10 L10,7');
      arrow1.setAttribute('stroke', '#1890FF');
      arrow1.setAttribute('stroke-width', '1.5');
      arrow1.setAttribute('fill', 'none');
      arrow1.setAttribute('stroke-linecap', 'round');
      arrow1.setAttribute('stroke-linejoin', 'round');

      // 右上到左下的箭头
      const arrow2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      arrow2.setAttribute('d', 'M10,2 L2,10 M5,7 L2,10 L2,7');
      arrow2.setAttribute('stroke', '#1890FF');
      arrow2.setAttribute('stroke-width', '1.5');
      arrow2.setAttribute('fill', 'none');
      arrow2.setAttribute('stroke-linecap', 'round');
      arrow2.setAttribute('stroke-linejoin', 'round');

      arrowIcon.appendChild(arrow1);
      arrowIcon.appendChild(arrow2);

      svg.appendChild(arrowIcon);

      console.log('[缩放调试] 手柄已添加到容器');
      console.log('[缩放调试] 双向对角箭头图标已添加');
      console.log('[缩放调试] 添加后容器的子元素数量:', svg.children.length);
      console.log('[缩放调试] 手动创建的缩放手柄:', rect);
      console.log('[缩放调试] 箭头图标:', arrowIcon);
      console.log('[缩放调试] 手柄位置和样式:', {
        x: rect.getAttribute('x'),
        y: rect.getAttribute('y'),
        width: rect.getAttribute('width'),
        height: rect.getAttribute('height'),
        fill: rect.getAttribute('fill'),
        stroke: rect.getAttribute('stroke'),
        cursor: rect.getAttribute('cursor')
      });
      console.log('[缩放调试] 手柄的父容器:', rect.parentElement);
      console.log('[缩放调试] 手柄是否可见:', rect.style.display !== 'none');
      // 重新尝试获取
      resizeHandle = rect;
    }

    if (!resizeHandle) {
      return;
    }
  }
  console.log('[缩放调试] 找到缩放手柄元素:', resizeHandle);

  // 鼠标按下事件
  const onMouseDown = (e) => {
    console.log('[循环缩放] 鼠标按下事件触发');
    console.log('[缩放调试] 事件对象:', e);
    console.log('[缩放调试] 事件目标:', e.target);
    e.preventDefault();
    e.stopPropagation();

    isResizing = true;
    const size = node.getSize();
    startSize = { width: size.width, height: size.height };
    startPosition = { x: e.clientX, y: e.clientY };

    console.log('[循环缩放] 初始尺寸:', startSize);
    console.log('[循环缩放] 初始位置:', startPosition);

    // 添加全局事件监听
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);

    // 添加视觉反馈
    resizeHandle.style.fill = '#40a9ff';
    resizeHandle.style.stroke = '#096dd9';
    resizeHandle.style.strokeWidth = '2';

    console.log('[循环缩放] 开始缩放模式');
  };

  // 鼠标移动事件
  const onMouseMove = (e) => {
    console.log('[缩放调试] 鼠标移动事件，isResizing:', isResizing);
    if (!isResizing) return;

    const deltaX = e.clientX - startPosition.x;
    const deltaY = e.clientY - startPosition.y;

    console.log('[缩放调试] 鼠标移动距离:', { deltaX, deltaY });
    console.log('[缩放调试] 当前鼠标位置:', { x: e.clientX, y: e.clientY });

    // 计算新的尺寸（最小尺寸限制）
    const newWidth = Math.max(300, startSize.width + deltaX);
    const newHeight = Math.max(150, startSize.height + deltaY);

    console.log('[缩放调试] 计算的新尺寸:', { newWidth, newHeight });

    // 调整节点大小
    try {
      node.resize(newWidth, newHeight);
      console.log('[缩放调试] 节点resize调用成功');
    } catch (error) {
      console.error('[缩放调试] 节点resize失败:', error);
    }

    // 同时调整内容区域大小
    const contentId = `${node.id}-content`;
    const contentNode = graph.getCellById(contentId);
    if (contentNode) {
      try {
        contentNode.resize(newWidth, newHeight + 50); // 内容区域稍大一些
        console.log('[缩放调试] 内容区域resize调用成功');
      } catch (error) {
        console.error('[缩放调试] 内容区域resize失败:', error);
      }
    } else {
      console.log('[缩放调试] 找不到内容区域节点:', contentId);
    }

    console.log('[缩放调试] 调整大小完成:', { width: newWidth, height: newHeight });
  };

  // 鼠标释放事件
  const onMouseUp = () => {
    if (!isResizing) return;

    isResizing = false;

    // 移除全局事件监听
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);

    // 恢复视觉反馈
    resizeHandle.style.fill = '#ffffff';
    resizeHandle.style.stroke = '#1890FF';
    resizeHandle.style.strokeWidth = '1.5';

    const finalSize = node.getSize();
    console.log('[循环缩放] 缩放完成:', finalSize);

    // 触发图表更新事件
    graph.trigger('node:resized', { node, size: finalSize });
  };

  // 绑定事件
  console.log('[缩放调试] 开始绑定事件到缩放手柄');
  resizeHandle.addEventListener('mousedown', onMouseDown);
  console.log('[缩放调试] mousedown事件已绑定');

  // 添加悬停效果
  resizeHandle.addEventListener('mouseenter', () => {
    console.log('[缩放调试] 鼠标进入缩放手柄');
    if (!isResizing) {
      resizeHandle.style.fill = '#e6f7ff';
      resizeHandle.style.stroke = '#40a9ff';
      resizeHandle.style.strokeWidth = '2';
    }
  });

  resizeHandle.addEventListener('mouseleave', () => {
    console.log('[缩放调试] 鼠标离开缩放手柄');
    if (!isResizing) {
      resizeHandle.style.fill = '#ffffff';
      resizeHandle.style.stroke = '#1890FF';
      resizeHandle.style.strokeWidth = '1.5';
    }
  });

  // 添加点击测试
  resizeHandle.addEventListener('click', () => {
    console.log('[缩放调试] 缩放手柄被点击');
  });

  console.log('[缩放调试] 所有事件绑定完成');

  // 监听手柄被移除的情况
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.removedNodes.forEach((removedNode) => {
          if (removedNode === resizeHandle) {
            console.log('[缩放调试] 手柄被从DOM中移除了！');
            console.log('[缩放调试] 移除操作的目标:', mutation.target);
          }
        });
      }
    });
  });

  if (resizeHandle.parentElement) {
    observer.observe(resizeHandle.parentElement, { childList: true, subtree: true });
    console.log('[缩放调试] 开始监听手柄的DOM变化');
  }

  // 测试手柄是否可交互
  setTimeout(() => {
    console.log('[缩放调试] 延迟检查手柄状态...');
    console.log('[缩放调试] 手柄在DOM中:', document.contains(resizeHandle));
    console.log('[缩放调试] 手柄的父元素:', resizeHandle.parentElement);
    console.log('[缩放调试] 手柄的计算样式:', window.getComputedStyle(resizeHandle));
    console.log('[缩放调试] 手柄的边界框:', resizeHandle.getBoundingClientRect());

    // 如果手柄不在DOM中，尝试重新添加
    if (!document.contains(resizeHandle)) {
      console.log('[缩放调试] 手柄不在DOM中，尝试重新添加...');
      const container = nodeView.container;
      if (container) {
        container.appendChild(resizeHandle);
        console.log('[缩放调试] 手柄已重新添加到容器');
        console.log('[缩放调试] 重新添加后手柄在DOM中:', document.contains(resizeHandle));
      }
    }

    // 尝试程序化触发事件测试
    const testEvent = new MouseEvent('mouseenter', { bubbles: true });
    resizeHandle.dispatchEvent(testEvent);
    console.log('[缩放调试] 程序化触发mouseenter事件');
  }, 1000);
}

/**
 * 开始调整循环内容区域大小
 * @param {Event} e - 鼠标事件
 * @param {Object} node - 节点对象
 * @param {Object} graph - 图表实例
 */
export function startResizeLoopContent(e, node, graph) {
  const startX = e.clientX;
  const startY = e.clientY;
  const startWidth = node.size().width;
  const startHeight = node.size().height;
  const loopId = node.getData().parentId;
  const loopNode = graph.getCellById(loopId);
  
  // 添加调整中的视觉反馈
  node.attr('body', {
    strokeWidth: 2,
    strokeDasharray: '5 5',
  });
  
  // 显示调整大小提示文本
  node.attr('resizeText', {
    opacity: 1
  });
  
  // 增加调整大小手柄的视觉反馈
  node.attr('resizeHandle', {
    fill: '#40a9ff',
    transform: 'scale(1.2)',
    filter: {
      name: 'dropShadow',
      args: {
        dx: 0,
        dy: 0,
        blur: 8,
        color: 'rgba(24, 144, 255, 0.6)',
      },
    }
  });
  
  // 增加图标的视觉反馈
  node.attr('resizeIcon', {
    strokeWidth: 3
  });
  
  // 创建鼠标移动和鼠标释放事件处理函数
  const onMouseMove = (moveEvent) => {
    const deltaX = moveEvent.clientX - startX;
    const deltaY = moveEvent.clientY - startY;
    
    // 计算新的尺寸，确保最小尺寸
    const newWidth = Math.max(300, startWidth + deltaX);
    const newHeight = Math.max(150, startHeight + deltaY);
    
    // 调整内容区域大小
    node.resize(newWidth, newHeight);
    
    // 同时调整循环节点的宽度
    if (loopNode) {
      loopNode.resize(newWidth, loopNode.size().height);
    }
    
    // 确保IterationItem节点保持在固定相对位置
    const iterationItemId = `${loopId}-item`;
    const iterationItem = graph.getCellById(iterationItemId);
    if (iterationItem) {
      const contentPos = node.position();
      iterationItem.position(
        contentPos.x + 60,  // 距离内容区域左边60px
        contentPos.y + 60   // 距离内容区域顶部60px
      );
    }
  };
  
  const onMouseUp = () => {
    // 恢复正常样式
    node.attr('body', {
      strokeWidth: 1,
      strokeDasharray: '5 5',
    });
    
    // 隐藏调整大小提示文本
    node.attr('resizeText', {
      opacity: 0
    });
    
    // 恢复调整大小手柄的正常样式
    node.attr('resizeHandle', {
      fill: '#1890FF',
      transform: 'none',
      filter: {
        name: 'dropShadow',
        args: {
          dx: 0,
          dy: 0,
          blur: 3,
          color: 'rgba(0, 0, 0, 0.3)',
        },
      }
    });
    
    // 恢复图标的正常样式
    node.attr('resizeIcon', {
      strokeWidth: 2
    });
    
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
  };
  
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
}

/**
 * 设置循环节点和内容区域的同步移动
 * @param {Object} graph - 图表实例
 */
export function setupLoopSyncMovement(graph) {
  let currentDraggingLoopNode = null;
  let currentDraggingLoopPartner = null;
  let dragStartPosition = null;
  
  // 监听节点移动开始
  graph.on('node:mousedown', ({ node }) => {
    const nodeData = node.getData();
    
    // 如果是循环节点或循环内容区域
    if (nodeData && (nodeData.isLoop || nodeData.isLoopContent)) {
      // 标记当前正在拖动的节点
      currentDraggingLoopNode = node;
      
      // 如果是循环头部节点
      if (nodeData.isLoop) {
        currentDraggingLoopPartner = graph.getCellById(`${node.id}-content`);
      } 
      // 如果是循环内容区域
      else if (nodeData.isLoopContent) {
        currentDraggingLoopPartner = graph.getCellById(nodeData.parentId);
      }
      
      // 记录初始位置
      dragStartPosition = node.position();
    }
  });
  
  // 监听节点移动
  graph.on('node:moving', ({ node }) => {
    // 如果当前拖动的是循环节点或循环内容区域，且有对应的伙伴节点
    if (node === currentDraggingLoopNode && currentDraggingLoopPartner) {
      const nodeData = node.getData();
      const currentPos = node.position();
      const deltaX = currentPos.x - dragStartPosition.x;
      const deltaY = currentPos.y - dragStartPosition.y;
      
      // 更新拖动起始位置
      dragStartPosition = currentPos;
      
      // 如果是循环头部节点，移动内容区域
      if (nodeData.isLoop) {
        const contentPos = currentDraggingLoopPartner.position();
        currentDraggingLoopPartner.position(
          contentPos.x + deltaX,
          contentPos.y + deltaY
        );
        
        // 同时移动循环项节点，保持在内容区域的固定位置
        const iterationItemNode = graph.getCellById(`${node.id}-item`);
        if (iterationItemNode) {
          // 计算循环内容区域的新位置
          const newContentX = contentPos.x + deltaX;
          const newContentY = contentPos.y + deltaY;
          
          // 将IterationItem放置在内容区域的固定相对位置
          iterationItemNode.position(
            newContentX + 60,  // 距离内容区域左边60px
            newContentY + 60   // 距离内容区域顶部60px
          );
        }
        
        // 同时移动所有子节点
        if (nodeData.childNodes && Array.isArray(nodeData.childNodes)) {
          nodeData.childNodes.forEach(childId => {
            // 跳过IterationItem节点，因为我们已经单独处理了
            if (childId === `${node.id}-item`) return;
            
            const childNode = graph.getCellById(childId);
            if (childNode) {
              const pos = childNode.position();
              childNode.position(pos.x + deltaX, pos.y + deltaY);
            }
          });
        }
      } 
      // 如果是循环内容区域，移动头部节点
      else if (nodeData.isLoopContent) {
        const headerPos = currentDraggingLoopPartner.position();
        currentDraggingLoopPartner.position(
          headerPos.x + deltaX,
          headerPos.y + deltaY
        );
        
        // 同时移动循环项节点，保持在内容区域的固定位置
        const iterationItemNode = graph.getCellById(`${nodeData.parentId}-item`);
        if (iterationItemNode) {
          // 计算循环内容区域的新位置
          const newContentX = currentPos.x + deltaX;
          const newContentY = currentPos.y + deltaY;
          
          // 将IterationItem放置在内容区域的固定相对位置
          iterationItemNode.position(
            newContentX + 60,  // 距离内容区域左边60px
            newContentY + 60   // 距离内容区域顶部60px
          );
        }
        
        // 同时移动所有子节点
        const headerData = currentDraggingLoopPartner.getData();
        if (headerData && headerData.childNodes && Array.isArray(headerData.childNodes)) {
          headerData.childNodes.forEach(childId => {
            // 跳过IterationItem节点，因为我们已经单独处理了
            if (childId === `${nodeData.parentId}-item`) return;
            
            const childNode = graph.getCellById(childId);
            if (childNode) {
              const pos = childNode.position();
              childNode.position(pos.x + deltaX, pos.y + deltaY);
            }
          });
        }
      }
    }
  });
  
  // 监听节点移动结束
  graph.on('node:mouseup', () => {
    currentDraggingLoopNode = null;
    currentDraggingLoopPartner = null;
    dragStartPosition = null;
  });
}

/**
 * 检查节点是否在循环内容区域内，并更新相应的数据
 * @param {Object} graph - 图表实例
 */
export function checkNodeInLoop(graph) {
  // 获取所有循环内容区域节点
  const loopContentNodes = graph.getNodes().filter(node => {
    const data = node.getData();
    return data && data.isLoopContent;
  });
  
  // 获取所有非循环节点和非循环内容区域节点
  const normalNodes = graph.getNodes().filter(node => {
    const data = node.getData();
    return !data || (!data.isLoop && !data.isLoopContent && !data.isIterationItem);
  });
  
  // 遍历所有普通节点
  normalNodes.forEach(node => {
    let isInsideAnyLoop = false;
    let containingLoopId = null;
    
    // 获取节点的位置和大小
    const nodeBBox = node.getBBox();
    
    // 检查节点是否在任何循环内容区域内
    loopContentNodes.forEach(loopContent => {
      const loopContentBBox = loopContent.getBBox();
      
      // 如果节点完全在循环内容区域内
      if (
        nodeBBox.x >= loopContentBBox.x &&
        nodeBBox.y >= loopContentBBox.y &&
        nodeBBox.x + nodeBBox.width <= loopContentBBox.x + loopContentBBox.width &&
        nodeBBox.y + nodeBBox.height <= loopContentBBox.y + loopContentBBox.height
      ) {
        isInsideAnyLoop = true;
        containingLoopId = loopContent.getData().parentId;
        
        // 更新节点视图，添加循环内标记
        const nodeView = graph.findViewByCell(node);
        if (nodeView && nodeView.container) {
          nodeView.container.classList.add('in-loop-content');
        }
        
        // 更新节点数据
        const nodeData = node.getData() || {};
        nodeData.inLoopId = containingLoopId;
        node.setData(nodeData);
        
        // 将节点添加到循环节点的子节点列表中
        const loopNode = graph.getCellById(containingLoopId);
        if (loopNode) {
          const loopData = loopNode.getData() || {};
          if (!loopData.childNodes) {
            loopData.childNodes = [];
          }
          
          // 检查节点是否已经在子节点列表中
          if (!loopData.childNodes.includes(node.id)) {
            loopData.childNodes.push(node.id);
            loopNode.setData(loopData);
          }
        }
      }
    });
    
    // 如果节点不在任何循环内，移除循环内标记
    if (!isInsideAnyLoop) {
      // 更新节点视图，移除循环内标记
      const nodeView = graph.findViewByCell(node);
      if (nodeView && nodeView.container) {
        nodeView.container.classList.remove('in-loop-content');
      }
      
      // 更新节点数据
      const nodeData = node.getData() || {};
      
      // 如果节点之前在循环内，从循环节点的子节点列表中移除
      if (nodeData.inLoopId) {
        const previousLoopNode = graph.getCellById(nodeData.inLoopId);
        if (previousLoopNode) {
          const loopData = previousLoopNode.getData() || {};
          if (loopData.childNodes) {
            const index = loopData.childNodes.indexOf(node.id);
            if (index !== -1) {
              loopData.childNodes.splice(index, 1);
              previousLoopNode.setData(loopData);
            }
          }
        }
      }
      
      // 清除节点的循环ID
      delete nodeData.inLoopId;
      node.setData(nodeData);
    }
  });
} 