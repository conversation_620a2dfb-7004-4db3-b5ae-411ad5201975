<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词Query保存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .step {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
        .success {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 10px;
            margin: 10px 0;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            font-size: 16px;
            margin-right: 8px;
        }
        .checklist li.checked:before {
            content: "✅ ";
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>关键词Query保存测试指南</h1>
        <p>确保关键词组件的query能够正确保存为期望的格式</p>

        <div class="test-section">
            <div class="test-title">🎯 目标格式</div>
            <div class="success">
                <strong>期望的query格式：</strong>
                <div class="code">
"query": [
  {
    "component_id": "Answer_0",
    "type": "reference"
  }
]
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 完整测试步骤</div>
            
            <div class="step">
                <strong>步骤1：打开关键词节点设置</strong>
                <ul class="checklist">
                    <li>双击关键词节点</li>
                    <li>确认设置抽屉打开</li>
                    <li>查看是否有"查询输入源"下拉框</li>
                </ul>
            </div>

            <div class="step">
                <strong>步骤2：检查可用组件</strong>
                <ul class="checklist">
                    <li>查看"查询输入源"下拉框是否有选项</li>
                    <li>如果没有选项，点击右侧的刷新按钮 🔄</li>
                    <li>确认看到类似"对话_0 (Answer_0)"的选项</li>
                </ul>
            </div>

            <div class="step">
                <strong>步骤3：选择查询输入源</strong>
                <ul class="checklist">
                    <li>在下拉框中选择"对话_0 (Answer_0)"</li>
                    <li>查看控制台日志，应该看到：</li>
                </ul>
                <div class="code">
[KeywordsSettings] selectedQueryComponentId变化: Answer_0
[KeywordsSettings] 更新query为: [{component_id: "Answer_0", type: "reference"}]
                </div>
            </div>

            <div class="step">
                <strong>步骤4：保存设置</strong>
                <ul class="checklist">
                    <li>点击"保存设置"按钮</li>
                    <li>查看控制台日志，应该看到完整的保存流程：</li>
                </ul>
                <div class="code">
[KeywordsSettings] 用户点击保存按钮
[KeywordsSettings] 保存的数据: {...}
[KeywordsSettings] 发送保存事件: {...}
[NodeDrawer] 处理Keywords节点保存数据: {...}
[NodeDrawer] Keywords节点form.query: [{component_id: "Answer_0", type: "reference"}]
[NodeDrawer] 处理后的form.query: [{component_id: "Answer_0", type: "reference"}]
[X6Graph] Keywords节点数据更新: {...}
保存节点设置: {...}
                </div>
            </div>

            <div class="step">
                <strong>步骤5：验证保存结果</strong>
                <ul class="checklist">
                    <li>关闭设置抽屉</li>
                    <li>重新打开关键词节点设置</li>
                    <li>确认"查询输入源"仍然选中Answer_0</li>
                </ul>
            </div>

            <div class="step">
                <strong>步骤6：测试DSL生成</strong>
                <ul class="checklist">
                    <li>点击运行按钮</li>
                    <li>查看DSL数据中的KeywordExtract_0</li>
                    <li>确认query不再是空数组</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 关键检查点</div>
            
            <div class="warning">
                <strong>检查点1：组件列表加载</strong>
                <p>如果"查询输入源"下拉框为空：</p>
                <ul>
                    <li>点击刷新按钮 🔄</li>
                    <li>检查控制台是否有错误信息</li>
                    <li>确认图表中有其他节点</li>
                </ul>
            </div>

            <div class="warning">
                <strong>检查点2：数据更新</strong>
                <p>选择组件后，必须看到以下日志：</p>
                <div class="code">
[KeywordsSettings] selectedQueryComponentId变化: Answer_0
[KeywordsSettings] 更新query为: [...]
                </div>
                <p>如果没有看到，说明watch没有触发</p>
            </div>

            <div class="warning">
                <strong>检查点3：保存流程</strong>
                <p>点击保存按钮后，必须看到完整的保存日志链</p>
                <p>如果中断在某个环节，说明该环节有问题</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚨 常见问题排查</div>
            
            <div class="error">
                <strong>问题1：下拉框为空</strong>
                <p><strong>原因：</strong>图表数据获取失败</p>
                <p><strong>解决：</strong>点击刷新按钮，检查图表连接</p>
            </div>

            <div class="error">
                <strong>问题2：选择后没有日志</strong>
                <p><strong>原因：</strong>watch没有正确触发</p>
                <p><strong>解决：</strong>检查selectedQueryComponentId的绑定</p>
            </div>

            <div class="error">
                <strong>问题3：保存后数据丢失</strong>
                <p><strong>原因：</strong>数据传递链中某个环节有问题</p>
                <p><strong>解决：</strong>根据日志定位具体环节</p>
            </div>

            <div class="error">
                <strong>问题4：DSL中仍然为空</strong>
                <p><strong>原因：</strong>图表节点数据没有正确更新</p>
                <p><strong>解决：</strong>检查X6Graph的updateNodeData方法</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 成功标志</div>
            <div class="success">
                <strong>如果一切正常，你应该看到：</strong>
                <ol>
                    <li>下拉框有可选组件</li>
                    <li>选择组件时有相应日志</li>
                    <li>保存时有完整的日志链</li>
                    <li>重新打开设置时选择保持</li>
                    <li>DSL中query格式正确：</li>
                </ol>
                <div class="code">
"query": [
  {
    "component_id": "Answer_0",
    "type": "reference"
  }
]
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 立即行动</div>
            <div class="step">
                <strong>现在请按照上述步骤操作：</strong>
                <ol>
                    <li>打开关键词节点设置</li>
                    <li>选择查询输入源</li>
                    <li>点击保存按钮</li>
                    <li>查看控制台日志</li>
                    <li>测试DSL生成</li>
                </ol>
                <p><strong>如果有任何问题，请告诉我具体在哪个步骤出现了问题，以及相关的日志信息。</strong></p>
            </div>
        </div>
    </div>
</body>
</html>
