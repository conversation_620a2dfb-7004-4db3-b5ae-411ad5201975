<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词数据流程调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .step {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
        .problem {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
        }
        .solution {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
        }
        .flow-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .flow-step {
            background: #f0f8ff;
            border: 2px solid #4a90e2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .flow-step.error {
            background: #ffe6e6;
            border-color: #ff4444;
        }
        .arrow {
            text-align: center;
            font-size: 24px;
            color: #4a90e2;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>关键词组件数据流程调试</h1>
        <p>分析关键词组件query为空的完整数据流程</p>

        <div class="debug-section">
            <div class="debug-title">🔍 问题现状</div>
            <div class="problem">
                <strong>问题：</strong>关键词组件的query在DSL中仍然为空数组
                <div class="code">
KeywordExtract_0: {
  obj: {
    params: {
      query: []  // ❌ 仍然是空数组
    }
  }
}
                </div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📊 数据流程分析</div>
            <div class="flow-diagram">
                <div class="flow-step">
                    <strong>1. 用户操作</strong><br>
                    在KeywordsSettings中<br>
                    选择查询输入源
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>2. 数据更新</strong><br>
                    selectedQueryComponentId变化<br>
                    触发watch更新nodeData.form.query
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>3. 用户保存</strong><br>
                    点击"保存设置"按钮<br>
                    触发saveSettings方法
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>4. 发送事件</strong><br>
                    KeywordsSettings发出save事件<br>
                    包含完整的nodeData
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <strong>5. NodeDrawer处理</strong><br>
                    接收save事件<br>
                    调用saveSettings方法
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step error">
                    <strong>6. 数据传递</strong><br>
                    发送到App.vue的<br>
                    saveNodeSettings方法
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step error">
                    <strong>7. 图表更新</strong><br>
                    调用X6Graph的<br>
                    updateNodeData方法
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step error">
                    <strong>8. DSL构建</strong><br>
                    buildComponentsFromGraph<br>
                    读取节点数据构建DSL
                </div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🔧 可能的问题点</div>
            
            <div class="step">
                <strong>问题点1：数据格式不匹配</strong>
                <p>KeywordsSettings发送的数据格式可能与X6Graph期望的格式不一致</p>
                <div class="code">
// KeywordsSettings发送的格式
{
  name: "关键词提取",
  selectedModel: "qwen-max@Tongyi-Qianwen",
  form: { query: [...] }
}

// X6Graph可能期望的格式
{
  data: {
    form: { query: [...] }
  }
}
                </div>
            </div>

            <div class="step">
                <strong>问题点2：NodeDrawer没有特殊处理</strong>
                <p>NodeDrawer只对Generation节点进行了特殊处理，没有处理Keywords节点</p>
                <div class="code">
// 当前代码只处理Generation节点
if (this.node.type === 'generation' && data.systemPrompt !== undefined) {
  // 特殊处理...
}
// ❌ 缺少对keywords节点的特殊处理
                </div>
            </div>

            <div class="step">
                <strong>问题点3：X6Graph数据更新问题</strong>
                <p>updateNodeData方法可能没有正确更新form.query字段</p>
            </div>

            <div class="step">
                <strong>问题点4：DSL构建时数据丢失</strong>
                <p>buildComponentsFromGraph可能没有正确读取更新后的节点数据</p>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🎯 调试方法</div>
            
            <div class="step">
                <strong>步骤1：验证KeywordsSettings数据</strong>
                <p>在saveSettings方法中添加详细日志，确认发送的数据正确</p>
                <div class="code">
console.log('[KeywordsSettings] 保存的form.query:', this.nodeData.form.query);
                </div>
            </div>

            <div class="step">
                <strong>步骤2：验证NodeDrawer数据传递</strong>
                <p>在NodeDrawer的saveSettings方法中添加日志</p>
                <div class="code">
console.log('[NodeDrawer] 接收到的数据:', data);
console.log('[NodeDrawer] 数据中的form.query:', data.form?.query);
                </div>
            </div>

            <div class="step">
                <strong>步骤3：验证X6Graph数据更新</strong>
                <p>在updateNodeData方法中添加日志</p>
                <div class="code">
console.log('[X6Graph] 更新前的节点数据:', currentData);
console.log('[X6Graph] 更新的数据:', data);
console.log('[X6Graph] 更新后的节点数据:', newData);
                </div>
            </div>

            <div class="step">
                <strong>步骤4：验证DSL构建</strong>
                <p>在buildComponentsFromGraph中添加关键词节点的特殊日志</p>
                <div class="code">
if (node.id.includes('KeywordExtract')) {
  console.log('[DSL构建] KeywordExtract节点数据:', node.data);
  console.log('[DSL构建] form.query:', node.data?.form?.query);
}
                </div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">💡 解决方案</div>
            
            <div class="solution">
                <strong>方案1：修复NodeDrawer数据处理</strong>
                <p>为Keywords节点添加特殊处理逻辑，确保数据格式正确</p>
                <div class="code">
if (this.node.type === 'keywords' || this.node.type === 'keywordNode') {
  processedData = {
    ...data,
    data: {
      ...this.node.data,
      form: {
        ...this.node.data.form,
        ...data.form
      }
    }
  };
}
                </div>
            </div>

            <div class="solution">
                <strong>方案2：统一数据格式</strong>
                <p>确保所有组件使用相同的数据格式进行保存</p>
            </div>

            <div class="solution">
                <strong>方案3：添加调试日志</strong>
                <p>在关键节点添加详细的调试日志，追踪数据流向</p>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🚀 立即行动</div>
            <div class="step">
                <strong>现在请执行以下操作：</strong>
                <ol>
                    <li>打开关键词节点设置</li>
                    <li>选择查询输入源</li>
                    <li>点击保存按钮</li>
                    <li>查看控制台中的详细日志</li>
                    <li>特别关注每个步骤中form.query的值</li>
                    <li>找出数据丢失的具体位置</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
