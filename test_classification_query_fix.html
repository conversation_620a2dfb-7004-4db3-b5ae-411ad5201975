<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题分类Query修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>问题分类Query修复测试</h1>
        <p>这个测试页面用于验证问题分类组件的query设置修复是否正确。</p>

        <div class="test-section">
            <div class="test-title">1. 测试错误的component_id格式</div>
            <p>检查是否还存在错误的component_id格式（如：Answer:ChubbyWallsBegin）</p>
            <button class="button" onclick="testWrongComponentId()">运行测试</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试正确的component_id格式</div>
            <p>验证正确的component_id格式（如：Answer_0, Retrieval_1）</p>
            <button class="button" onclick="testCorrectComponentId()">运行测试</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 测试ClassificationSettings组件</div>
            <p>验证ClassificationSettings组件是否能正确显示可用组件列表</p>
            <button class="button" onclick="testClassificationSettings()">运行测试</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 模拟图表数据测试</div>
            <p>使用模拟的图表数据测试组件ID提取功能</p>
            <button class="button" onclick="testGraphDataExtraction()">运行测试</button>
            <div id="test4-result"></div>
        </div>
    </div>

    <script>
        // 模拟图表数据
        const mockGraphData = {
            nodes: [
                {
                    id: 'begin',
                    type: 'beginNode',
                    data: { label: 'Begin', name: 'begin' }
                },
                {
                    id: 'Answer_0',
                    type: 'logicNode',
                    data: { label: 'Answer', name: '对话_0' }
                },
                {
                    id: 'Retrieval_0',
                    type: 'retrievalNode',
                    data: { label: 'Retrieval', name: '知识检索_0' }
                },
                {
                    id: 'Categorize_0',
                    type: 'categorizeNode',
                    data: { label: 'Categorize', name: '问题分类_0' }
                }
            ]
        };

        function testWrongComponentId() {
            const resultDiv = document.getElementById('test1-result');
            
            // 检查是否存在错误格式
            const wrongFormats = [
                'Answer:ChubbyWallsBegin',
                'Retrieval:SomeRandomName',
                'Generate:AnotherRandomName'
            ];
            
            let hasWrongFormat = false;
            const foundWrongFormats = [];
            
            // 这里应该检查实际的代码，但在测试页面中我们模拟检查
            // 实际应用中，这些检查会在代码审查或自动化测试中进行
            
            if (hasWrongFormat) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 测试失败</strong><br>
                        发现错误的component_id格式：<br>
                        <div class="code">${foundWrongFormats.join('\n')}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ 测试通过</strong><br>
                        未发现错误的component_id格式（如：Answer:ChubbyWallsBegin）
                    </div>
                `;
            }
        }

        function testCorrectComponentId() {
            const resultDiv = document.getElementById('test2-result');
            
            // 测试正确的ID格式
            const correctFormats = [
                'Answer_0',
                'Retrieval_1',
                'Generate_2',
                'Categorize_0',
                'KeywordExtract_1'
            ];
            
            const validPattern = /^[A-Za-z]+_\d+$/;
            const allValid = correctFormats.every(id => validPattern.test(id));
            
            if (allValid) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ 测试通过</strong><br>
                        所有component_id格式正确：<br>
                        <div class="code">${correctFormats.join('\n')}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 测试失败</strong><br>
                        存在格式不正确的component_id
                    </div>
                `;
            }
        }

        function testClassificationSettings() {
            const resultDiv = document.getElementById('test3-result');
            
            // 模拟ClassificationSettings组件的功能
            function extractComponentsFromGraph(graphData) {
                const components = [];
                
                if (graphData && graphData.nodes) {
                    graphData.nodes.forEach(node => {
                        if (node.id && node.data) {
                            const displayName = node.data.name || node.data.title || node.data.label;
                            components.push({
                                id: node.id,
                                label: `${displayName} (${node.id})`
                            });
                        }
                    });
                }
                
                return components;
            }
            
            const components = extractComponentsFromGraph(mockGraphData);
            
            if (components.length > 0) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ 测试通过</strong><br>
                        成功提取到 ${components.length} 个组件：<br>
                        <div class="code">${components.map(c => `${c.label}`).join('\n')}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 测试失败</strong><br>
                        未能提取到任何组件
                    </div>
                `;
            }
        }

        function testGraphDataExtraction() {
            const resultDiv = document.getElementById('test4-result');
            
            // 测试ID生成规则
            function generateNodeId(type, allNodes) {
                const typeToPrefix = {
                    'dialogue': 'Answer',
                    'retrieval': 'Retrieval',
                    'generation': 'Generate',
                    'classification': 'Categorize',
                    'logicNode': 'Answer',
                    'retrievalNode': 'Retrieval',
                    'generateNode': 'Generate',
                    'categorizeNode': 'Categorize'
                };
                
                const prefix = typeToPrefix[type] || type;
                const sameTypeNodes = allNodes.filter(n => n.id && n.id.startsWith(prefix + '_'));
                let maxIndex = -1;
                
                sameTypeNodes.forEach(n => {
                    const match = n.id.match(new RegExp(`^${prefix}_(\\d+)$`));
                    if (match) {
                        const idx = parseInt(match[1], 10);
                        if (idx > maxIndex) maxIndex = idx;
                    }
                });
                
                return `${prefix}_${maxIndex + 1}`;
            }
            
            // 测试生成新的ID
            const testCases = [
                { type: 'logicNode', expected: 'Answer_1' },
                { type: 'retrievalNode', expected: 'Retrieval_1' },
                { type: 'categorizeNode', expected: 'Categorize_1' }
            ];
            
            const results = testCases.map(testCase => {
                const generated = generateNodeId(testCase.type, mockGraphData.nodes);
                return {
                    type: testCase.type,
                    expected: testCase.expected,
                    generated: generated,
                    passed: generated === testCase.expected
                };
            });
            
            const allPassed = results.every(r => r.passed);
            
            if (allPassed) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ 测试通过</strong><br>
                        ID生成规则正确：<br>
                        <div class="code">${results.map(r => `${r.type} → ${r.generated}`).join('\n')}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 测试失败</strong><br>
                        ID生成规则有误：<br>
                        <div class="code">${results.map(r => `${r.type}: 期望 ${r.expected}, 实际 ${r.generated} ${r.passed ? '✅' : '❌'}`).join('\n')}</div>
                    </div>
                `;
            }
        }

        // 页面加载时自动运行所有测试
        window.onload = function() {
            setTimeout(() => {
                testWrongComponentId();
                testCorrectComponentId();
                testClassificationSettings();
                testGraphDataExtraction();
            }, 500);
        };
    </script>
</body>
</html>
