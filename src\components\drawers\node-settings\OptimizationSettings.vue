<template>
  <div class="optimization-settings">
    <div class="form-section">
      <h4>节点标题</h4>
      <el-input 
        v-model="nodeData.name" 
        placeholder="节点标题" 
        style="margin-bottom: 15px"
      ></el-input>
    </div>
    
    <div class="form-section">
      <h4>选择使用的模型</h4>
      <el-select
        v-model="nodeData.form.llm_id"
        placeholder="选择模型"
        style="width: 100%"
        @change="handleModelChange"
      >
        <el-option label="qwen-max@Tongyi-Qianwen" value="qwen-max@Tongyi-Qianwen"></el-option>
        <el-option label="GPT-4" value="GPT-4"></el-option>
        <el-option label="Claude 3" value="Claude 3"></el-option>
        <el-option label="Llama 3" value="Llama 3"></el-option>
        <el-option label="Gemini" value="Gemini"></el-option>
      </el-select>
    </div>

    <div class="form-section">
      <h4>语言设置</h4>
      <el-input
        v-model="nodeData.form.language"
        placeholder="语言（可选）"
        style="margin-bottom: 15px"
      ></el-input>
    </div>

    <div class="form-section">
      <h4>消息历史窗口大小</h4>
      <el-input-number
        v-model="nodeData.form.message_history_window_size"
        :min="1"
        :max="50"
        style="width: 100%;"
      ></el-input-number>
    </div>

    <div class="form-section">
      <h4>温度 (Temperature)</h4>
      <el-switch v-model="nodeData.form.temperatureEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-slider
        v-if="nodeData.form.temperatureEnabled"
        v-model="nodeData.form.temperature"
        :min="0"
        :max="2"
        :step="0.1"
        :disabled="!nodeData.form.temperatureEnabled"
      ></el-slider>
    </div>

    <div class="form-section">
      <h4>最大令牌数 (Max Tokens)</h4>
      <el-switch v-model="nodeData.form.maxTokensEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-input-number
        v-if="nodeData.form.maxTokensEnabled"
        v-model="nodeData.form.max_tokens"
        :min="1"
        :max="4096"
        :disabled="!nodeData.form.maxTokensEnabled"
        style="width: 100%;"
      ></el-input-number>
    </div>
    
    <div class="form-section">
      <h4>优化设置</h4>
      <el-switch
        v-model="nodeData.settings.correctSpelling"
        active-text="拼写纠正"
      ></el-switch>
      <el-switch
        v-model="nodeData.settings.expandQuery"
        active-text="扩展查询"
        style="margin-top: 10px"
      ></el-switch>
    </div>

    <!-- 保存按钮 -->
    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OptimizationSettings',
  props: {
    initialData: {
      type: Object,
      default: () => ({
        name: '问题优化',
        selectedModel: '默认大模型',
        settings: {
          correctSpelling: true,
          expandQuery: false
        }
      })
    }
  },
  data() {
    return {
      nodeData: {
        name: '',
        selectedModel: 'qwen-max@Tongyi-Qianwen',
        settings: {
          correctSpelling: true,
          expandQuery: false
        },
        form: {
          frequencyPenaltyEnabled: false,
          frequency_penalty: 0.7,
          language: "",
          llm_id: "qwen-max@Tongyi-Qianwen",
          maxTokensEnabled: false,
          max_tokens: 256,
          message_history_window_size: 6,
          presencePenaltyEnabled: false,
          presence_penalty: 0.4,
          temperature: 0.1,
          temperatureEnabled: false,
          topPEnabled: false,
          top_p: 0.3
        }
      }
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        this.nodeData = {
          name: newVal.name || '问题优化',
          selectedModel: newVal.selectedModel || newVal.form?.llm_id || 'qwen-max@Tongyi-Qianwen',
          settings: {
            correctSpelling: newVal.settings?.correctSpelling !== undefined ? newVal.settings.correctSpelling : true,
            expandQuery: newVal.settings?.expandQuery !== undefined ? newVal.settings.expandQuery : false
          },
          form: {
            frequencyPenaltyEnabled: newVal.form?.frequencyPenaltyEnabled || false,
            frequency_penalty: newVal.form?.frequency_penalty || 0.7,
            language: newVal.form?.language || "",
            llm_id: newVal.form?.llm_id || newVal.selectedModel || "qwen-max@Tongyi-Qianwen",
            maxTokensEnabled: newVal.form?.maxTokensEnabled || false,
            max_tokens: newVal.form?.max_tokens || 256,
            message_history_window_size: newVal.form?.message_history_window_size || 6,
            presencePenaltyEnabled: newVal.form?.presencePenaltyEnabled || false,
            presence_penalty: newVal.form?.presence_penalty || 0.4,
            temperature: newVal.form?.temperature || 0.1,
            temperatureEnabled: newVal.form?.temperatureEnabled || false,
            topPEnabled: newVal.form?.topPEnabled || false,
            top_p: newVal.form?.top_p || 0.3
          }
        };
      },
      immediate: true,
      deep: true
    },
    nodeData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    }
  },
  methods: {
    // 保存设置
    saveSettings() {
      console.log('[OptimizationSettings] 用户点击保存按钮');
      console.log('[OptimizationSettings] 保存的数据:', this.nodeData);

      // 构建保存数据
      const saveData = {
        name: this.nodeData.name,
        selectedModel: this.nodeData.selectedModel,
        settings: this.nodeData.settings,
        form: this.nodeData.form
      };

      console.log('[OptimizationSettings] 发送保存事件:', saveData);
      this.$emit('save', saveData);

      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    },

    handleModelChange(model) {
      // 同时更新 selectedModel 和 form.llm_id
      this.nodeData.selectedModel = model;
      this.nodeData.form.llm_id = model;
      this.$emit('model-change', model);
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}
</style>