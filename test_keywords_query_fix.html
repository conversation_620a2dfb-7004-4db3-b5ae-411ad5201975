<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词组件Query修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 15px 0;
        }
        .before, .after {
            padding: 10px;
            border-radius: 4px;
        }
        .before {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .after {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>关键词组件Query修复测试</h1>
        <p>这个页面用于验证关键词组件的query设置修复是否正确。</p>

        <div class="test-section">
            <div class="test-title">1. 修复前后对比</div>
            <p>对比修复前后的关键词组件配置：</p>
            <button class="button" onclick="showBeforeAfter()">显示对比</button>
            <div id="comparison-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试组件数据格式</div>
            <p>验证关键词组件的数据格式是否正确：</p>
            <button class="button" onclick="testComponentFormat()">测试数据格式</button>
            <div id="format-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 测试Query设置功能</div>
            <p>模拟关键词组件的query设置功能：</p>
            <button class="button" onclick="testQuerySetting()">测试Query设置</button>
            <div id="query-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 使用说明</div>
            <div class="code">
使用步骤：
1. 创建关键词节点
2. 打开关键词节点设置
3. 在"查询输入源"下拉框中选择上游组件
4. 如果下拉框为空，点击刷新按钮
5. 保存设置

修复内容：
✅ 添加了查询输入源选择界面
✅ 修复了默认query为空数组
✅ 添加了刷新按钮和错误提示
✅ 支持多种图表数据获取方案
✅ 添加了详细的调试日志

技术细节：
- 组件层级：App.vue → NodeDrawer.vue → KeywordsSettings.vue
- 数据传递：graphData prop + 事件通信
- ID格式：Answer_0, Retrieval_1 等标准格式
- 容错机制：5种不同的数据获取方案
            </div>
        </div>
    </div>

    <script>
        function showBeforeAfter() {
            const resultDiv = document.getElementById('comparison-result');
            
            const beforeConfig = {
                component_name: "KeywordExtract",
                params: {
                    query: [
                        {
                            component_id: "Answer_0", // 硬编码，可能不正确
                            type: "reference"
                        }
                    ]
                }
            };

            const afterConfig = {
                component_name: "KeywordExtract", 
                params: {
                    query: [] // 初始为空，由用户选择
                }
            };

            resultDiv.innerHTML = `
                <div class="comparison">
                    <div class="before">
                        <h4>修复前 ❌</h4>
                        <div class="code">${JSON.stringify(beforeConfig, null, 2)}</div>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>硬编码component_id</li>
                            <li>可能引用不存在的组件</li>
                            <li>用户无法选择输入源</li>
                            <li>导致对话失败</li>
                        </ul>
                    </div>
                    <div class="after">
                        <h4>修复后 ✅</h4>
                        <div class="code">${JSON.stringify(afterConfig, null, 2)}</div>
                        <p><strong>改进：</strong></p>
                        <ul>
                            <li>初始query为空数组</li>
                            <li>用户可以选择输入源</li>
                            <li>自动使用正确的component_id</li>
                            <li>提供刷新和错误提示</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        function testComponentFormat() {
            const resultDiv = document.getElementById('format-result');
            
            // 模拟正确的组件数据格式
            const correctFormat = {
                "KeywordExtract_0": {
                    "downstream": ["Categorize_0"],
                    "obj": {
                        "component_name": "KeywordExtract",
                        "inputs": [],
                        "output": null,
                        "params": {
                            "llm_id": "qwen-max@Tongyi-Qianwen",
                            "query": [
                                {
                                    "component_id": "Answer_0",
                                    "type": "reference"
                                }
                            ],
                            "top_n": 5,
                            "temperature": 0.1
                        }
                    },
                    "upstream": ["Answer_0"]
                }
            };

            // 验证格式
            const isValid = validateComponentFormat(correctFormat);
            
            if (isValid.valid) {
                resultDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ 数据格式正确</strong><br>
                        关键词组件的数据格式符合RAGFlow标准：<br>
                        <div class="code">${JSON.stringify(correctFormat, null, 2)}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 数据格式错误</strong><br>
                        错误信息：${isValid.errors.join(', ')}
                    </div>
                `;
            }
        }

        function validateComponentFormat(data) {
            const errors = [];
            
            for (const [nodeId, component] of Object.entries(data)) {
                // 检查基本结构
                if (!component.obj || !component.obj.component_name) {
                    errors.push(`${nodeId}: 缺少component_name`);
                }
                
                if (!component.obj.params) {
                    errors.push(`${nodeId}: 缺少params`);
                }
                
                // 检查query格式
                if (component.obj.params && component.obj.params.query) {
                    const query = component.obj.params.query;
                    if (!Array.isArray(query)) {
                        errors.push(`${nodeId}: query必须是数组`);
                    } else {
                        query.forEach((q, index) => {
                            if (!q.component_id || !q.type) {
                                errors.push(`${nodeId}: query[${index}]格式不正确`);
                            }
                        });
                    }
                }
            }
            
            return {
                valid: errors.length === 0,
                errors: errors
            };
        }

        function testQuerySetting() {
            const resultDiv = document.getElementById('query-result');
            
            // 模拟可用组件列表
            const availableComponents = [
                { id: 'begin', label: 'begin (begin)' },
                { id: 'Answer_0', label: '对话_0 (Answer_0)' },
                { id: 'Retrieval_0', label: '知识检索_0 (Retrieval_0)' },
                { id: 'Generate_0', label: '生成回答_0 (Generate_0)' }
            ];
            
            // 模拟用户选择
            const selectedComponentId = 'Answer_0';
            
            // 生成query配置
            const queryConfig = selectedComponentId ? [{
                component_id: selectedComponentId,
                type: "reference"
            }] : [];
            
            resultDiv.innerHTML = `
                <div class="test-result success">
                    <strong>✅ Query设置功能正常</strong><br>
                    <p><strong>可用组件：</strong></p>
                    <div class="code">${availableComponents.map(c => c.label).join('\n')}</div>
                    <p><strong>用户选择：</strong> ${selectedComponentId}</p>
                    <p><strong>生成的query配置：</strong></p>
                    <div class="code">${JSON.stringify(queryConfig, null, 2)}</div>
                </div>
            `;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                showBeforeAfter();
            }, 500);
        };
    </script>
</body>
</html>
